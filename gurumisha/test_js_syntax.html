<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Syntax Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>JavaScript Syntax Test Results</h1>
    
    <div id="test-results"></div>

    <!-- Load the fixed toast manager -->
    <script src="/static/js/toast-manager.js"></script>
    
    <!-- Load CropperJS to test integrity fix -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <script>
        const results = document.getElementById('test-results');
        
        function addResult(message, type) {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            results.appendChild(div);
        }

        // Test 1: Toast Manager Loading
        try {
            if (window.toastManagerLoaded) {
                addResult('✓ Toast Manager loaded without syntax errors', 'success');
            } else {
                addResult('⚠ Toast Manager not loaded', 'info');
            }
        } catch (error) {
            addResult('✗ Toast Manager Error: ' + error.message, 'error');
        }

        // Test 2: CropperJS Loading
        setTimeout(() => {
            try {
                if (typeof Cropper !== 'undefined') {
                    addResult('✓ CropperJS loaded successfully (no integrity errors)', 'success');
                } else {
                    addResult('⚠ CropperJS not loaded yet', 'info');
                }
            } catch (error) {
                addResult('✗ CropperJS Error: ' + error.message, 'error');
            }
        }, 2000);

        // Test 3: Basic JavaScript Syntax
        try {
            // Test arrow functions
            const testArrow = () => 'test';
            
            // Test template literals
            const testTemplate = `Hello ${testArrow()}`;
            
            // Test destructuring
            const { test } = { test: true };
            
            // Test async/await syntax (without actually using it)
            const testAsync = async function() { return 'async'; };
            
            if (testTemplate === 'Hello test' && test === true) {
                addResult('✓ Modern JavaScript syntax working correctly', 'success');
            } else {
                addResult('✗ JavaScript syntax test failed', 'error');
            }
        } catch (error) {
            addResult('✗ JavaScript Syntax Error: ' + error.message, 'error');
        }

        // Test 4: Error Handling
        window.addEventListener('error', function(e) {
            addResult('✗ Runtime Error Detected: ' + e.error.message + ' at line ' + e.lineno, 'error');
        });

        // Test 5: Console Errors
        const originalConsoleError = console.error;
        console.error = function(...args) {
            addResult('⚠ Console Error: ' + args.join(' '), 'error');
            originalConsoleError.apply(console, args);
        };

        // Initial success message
        addResult('JavaScript syntax test initialized successfully', 'success');
        
        // Test toast functionality if available
        setTimeout(() => {
            try {
                if (window.toastManager && typeof window.toastManager.show === 'function') {
                    window.toastManager.show('Test toast from syntax checker', 'success');
                    addResult('✓ Toast Manager functionality working', 'success');
                } else if (typeof window.showToast === 'function') {
                    window.showToast('Test toast from syntax checker', 'success');
                    addResult('✓ Fallback toast functionality working', 'success');
                } else {
                    addResult('⚠ No toast functionality available', 'info');
                }
            } catch (error) {
                addResult('✗ Toast Functionality Error: ' + error.message, 'error');
            }
        }, 3000);
    </script>
</body>
</html>
