<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Car Edit Form</title>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
        button { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .error { color: red; margin-top: 5px; }
        .success { color: green; margin-top: 5px; }
        #response { margin-top: 20px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9; }
    </style>
</head>
<body>
    <h1>Debug Car Edit Form</h1>
    
    <div id="response"></div>
    
    <form hx-post="/dashboard/admin/car/1/edit/" 
          hx-target="#response"
          hx-on::before-request="console.log('HTMX request starting...', event)"
          hx-on::after-request="console.log('HTMX request completed...', event)">
        
        <input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">
        
        <div class="form-group">
            <label>Title:</label>
            <input type="text" name="title" value="Test Car Update" required>
        </div>
        
        <div class="form-group">
            <label>Price:</label>
            <input type="number" name="price" value="1500000" required>
        </div>
        
        <div class="form-group">
            <label>Brand:</label>
            <select name="brand" required>
                <option value="1">Toyota</option>
                <option value="2">Honda</option>
                <option value="3">Nissan</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Model:</label>
            <select name="model" required>
                <option value="1">Camry</option>
                <option value="2">Corolla</option>
                <option value="3">Prius</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Year:</label>
            <input type="number" name="year" value="2020" min="1900" max="2025" required>
        </div>
        
        <div class="form-group">
            <label>Color:</label>
            <input type="text" name="color" value="Black" required>
        </div>
        
        <div class="form-group">
            <label>Condition:</label>
            <select name="condition" required>
                <option value="new">New</option>
                <option value="used">Used</option>
                <option value="certified">Certified Pre-Owned</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Mileage:</label>
            <input type="number" name="mileage" value="50000" min="0" required>
        </div>
        
        <div class="form-group">
            <label>Fuel Type:</label>
            <select name="fuel_type" required>
                <option value="petrol">Petrol</option>
                <option value="diesel">Diesel</option>
                <option value="hybrid">Hybrid</option>
                <option value="electric">Electric</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Transmission:</label>
            <select name="transmission" required>
                <option value="manual">Manual</option>
                <option value="automatic">Automatic</option>
                <option value="cvt">CVT</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Engine Size:</label>
            <input type="number" name="engine_size" value="2.0" step="0.1" min="0.1">
        </div>
        
        <div class="form-group">
            <label>Listing Type:</label>
            <select name="listing_type" required>
                <option value="local">Local Listing</option>
                <option value="imported">Imported Car</option>
                <option value="sell_behalf">Sell on Behalf</option>
                <option value="auction">Auctioned</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Description:</label>
            <textarea name="description" rows="3">Test car description</textarea>
        </div>
        
        <div class="form-group">
            <label>Features:</label>
            <textarea name="features" rows="2">Air conditioning, Power steering, ABS</textarea>
        </div>
        
        <div class="form-group">
            <label>Status:</label>
            <select name="status" required>
                <option value="available">Available</option>
                <option value="sold">Sold</option>
                <option value="pending">Pending</option>
                <option value="featured">Featured</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Star Rating:</label>
            <input type="number" name="star_rating" value="4" min="0" max="5">
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" name="is_approved" value="true" checked> Approved
            </label>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" name="negotiable" value="true"> Negotiable
            </label>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" name="is_hot_deal" value="true"> Hot Deal
            </label>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" name="is_featured" value="true"> Featured
            </label>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" name="is_certified" value="true"> Certified
            </label>
        </div>
        
        <button type="submit">Update Car</button>
    </form>
    
    <script>
        // Add debugging
        document.addEventListener('htmx:beforeRequest', function(event) {
            console.log('HTMX Before Request:', event.detail);
        });
        
        document.addEventListener('htmx:afterRequest', function(event) {
            console.log('HTMX After Request:', event.detail);
            console.log('Response:', event.detail.xhr.responseText);
        });
        
        document.addEventListener('htmx:responseError', function(event) {
            console.error('HTMX Response Error:', event.detail);
        });
    </script>
</body>
</html>
