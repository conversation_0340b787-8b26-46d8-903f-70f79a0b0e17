# Generated by Django 5.2 on 2025-07-12 19:44

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0021_vehiclecondition_alter_carbrand_options_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='car',
            name='brand_name',
            field=models.CharField(blank=True, help_text='Brand name when not using database brands', max_length=100),
        ),
        migrations.AddField(
            model_name='car',
            name='condition_name',
            field=models.CharField(blank=True, help_text='Condition when not using database conditions', max_length=50),
        ),
        migrations.AddField(
            model_name='car',
            name='model_name',
            field=models.CharField(blank=True, help_text='Model name when not using database models', max_length=100),
        ),
        migrations.AlterField(
            model_name='car',
            name='brand',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.carbrand'),
        ),
        migrations.AlterField(
            model_name='car',
            name='model',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.carmodel'),
        ),
    ]
