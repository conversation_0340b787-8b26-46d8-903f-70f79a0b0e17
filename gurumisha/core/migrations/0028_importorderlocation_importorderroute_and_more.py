# Generated by Django 5.2 on 2025-07-14 19:06

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0027_payment_import_order_alter_payment_order'),
    ]

    operations = [
        migrations.CreateModel(
            name='ImportOrderLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('location_type', models.CharField(choices=[('origin', 'Origin Location'), ('auction_house', 'Auction House'), ('departure_port', 'Departure Port'), ('transit_port', 'Transit Port'), ('arrival_port', 'Arrival Port'), ('customs_facility', 'Customs Facility'), ('registration_office', 'Registration Office'), ('dispatch_center', 'Dispatch Center'), ('delivery_address', 'Delivery Address'), ('current_position', 'Current Position')], max_length=20)),
                ('name', models.Char<PERSON>ield(help_text='Human-readable location name', max_length=200)),
                ('description', models.TextField(blank=True, help_text='Additional location details')),
                ('latitude', models.DecimalField(decimal_places=7, help_text='Latitude coordinate', max_digits=10)),
                ('longitude', models.DecimalField(decimal_places=7, help_text='Longitude coordinate', max_digits=10)),
                ('altitude', models.DecimalField(blank=True, decimal_places=2, help_text='Altitude in meters', max_digits=8, null=True)),
                ('accuracy', models.CharField(choices=[('high', 'High (GPS)'), ('medium', 'Medium (Network)'), ('low', 'Low (Estimated)'), ('manual', 'Manual Entry')], default='manual', max_length=10)),
                ('address', models.TextField(blank=True)),
                ('city', models.CharField(blank=True, max_length=100)),
                ('state_province', models.CharField(blank=True, max_length=100)),
                ('country', models.CharField(blank=True, max_length=100)),
                ('postal_code', models.CharField(blank=True, max_length=20)),
                ('estimated_arrival_time', models.DateTimeField(blank=True, null=True)),
                ('actual_arrival_time', models.DateTimeField(blank=True, null=True)),
                ('estimated_departure_time', models.DateTimeField(blank=True, null=True)),
                ('actual_departure_time', models.DateTimeField(blank=True, null=True)),
                ('is_current_location', models.BooleanField(default=False, help_text='Is this the current location?')),
                ('is_waypoint', models.BooleanField(default=False, help_text='Is this a route waypoint?')),
                ('is_customer_visible', models.BooleanField(default=True, help_text='Show to customer?')),
                ('contact_person', models.CharField(blank=True, max_length=200)),
                ('contact_phone', models.CharField(blank=True, max_length=20)),
                ('contact_email', models.EmailField(blank=True, max_length=254)),
                ('facility_hours', models.CharField(blank=True, max_length=200)),
                ('special_instructions', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Import Order Location',
                'verbose_name_plural': 'Import Order Locations',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ImportOrderRoute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('route_name', models.CharField(help_text='Descriptive name for the route', max_length=200)),
                ('route_type', models.CharField(choices=[('sea_freight', 'Sea Freight'), ('air_freight', 'Air Freight'), ('land_transport', 'Land Transport'), ('multimodal', 'Multimodal Transport')], max_length=20)),
                ('route_status', models.CharField(choices=[('planned', 'Planned'), ('active', 'Active'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('delayed', 'Delayed')], default='planned', max_length=20)),
                ('total_distance_km', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('estimated_duration_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True)),
                ('actual_duration_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True)),
                ('planned_start_time', models.DateTimeField(blank=True, null=True)),
                ('actual_start_time', models.DateTimeField(blank=True, null=True)),
                ('planned_end_time', models.DateTimeField(blank=True, null=True)),
                ('actual_end_time', models.DateTimeField(blank=True, null=True)),
                ('vessel_name', models.CharField(blank=True, max_length=200)),
                ('vessel_imo', models.CharField(blank=True, help_text='International Maritime Organization number', max_length=20)),
                ('transport_company', models.CharField(blank=True, max_length=200)),
                ('transport_reference', models.CharField(blank=True, max_length=100)),
                ('auto_update_enabled', models.BooleanField(default=True, help_text='Enable automatic position updates')),
                ('tracking_interval_minutes', models.PositiveIntegerField(default=60, help_text='How often to update position')),
                ('route_notes', models.TextField(blank=True)),
                ('special_handling_requirements', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Import Order Route',
                'verbose_name_plural': 'Import Order Routes',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='LocationTrackingHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('latitude', models.DecimalField(decimal_places=7, max_digits=10)),
                ('longitude', models.DecimalField(decimal_places=7, max_digits=10)),
                ('altitude', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True)),
                ('tracking_source', models.CharField(choices=[('gps', 'GPS Device'), ('manual', 'Manual Entry'), ('api', 'External API'), ('estimated', 'Estimated Position'), ('vessel_tracking', 'Vessel Tracking'), ('customs_update', 'Customs Update')], max_length=20)),
                ('speed', models.DecimalField(blank=True, decimal_places=2, help_text='Speed in km/h', max_digits=6, null=True)),
                ('heading', models.DecimalField(blank=True, decimal_places=2, help_text='Direction in degrees', max_digits=6, null=True)),
                ('accuracy_radius', models.DecimalField(blank=True, decimal_places=2, help_text='Accuracy radius in meters', max_digits=8, null=True)),
                ('status_at_time', models.CharField(choices=[('import_request', 'Import Request'), ('auction_won', 'Auction Won'), ('shipped', 'Shipped'), ('in_transit', 'In Transit'), ('arrived_docked', 'Arrived - Docked'), ('under_clearance', 'Under Clearance'), ('registered', 'Registered'), ('ready_for_dispatch', 'Ready for Dispatch'), ('delivered', 'Delivered'), ('cancelled', 'Cancelled')], max_length=30)),
                ('notes', models.TextField(blank=True)),
                ('recorded_at', models.DateTimeField(help_text='When this position was recorded')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Location Tracking History',
                'verbose_name_plural': 'Location Tracking Histories',
                'ordering': ['-recorded_at'],
            },
        ),
        migrations.CreateModel(
            name='RouteWaypoint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('waypoint_type', models.CharField(choices=[('departure', 'Departure Point'), ('transit', 'Transit Point'), ('checkpoint', 'Checkpoint'), ('customs', 'Customs Point'), ('arrival', 'Arrival Point'), ('delivery', 'Delivery Point')], max_length=20)),
                ('sequence_order', models.PositiveIntegerField(help_text='Order in the route sequence')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('is_current', models.BooleanField(default=False, help_text='Is this the current waypoint?')),
                ('is_completed', models.BooleanField(default=False, help_text='Has this waypoint been reached?')),
                ('is_mandatory', models.BooleanField(default=True, help_text='Is this waypoint mandatory?')),
                ('estimated_arrival', models.DateTimeField(blank=True, null=True)),
                ('actual_arrival', models.DateTimeField(blank=True, null=True)),
                ('estimated_departure', models.DateTimeField(blank=True, null=True)),
                ('actual_departure', models.DateTimeField(blank=True, null=True)),
                ('distance_from_previous_km', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('estimated_duration_from_previous_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True)),
                ('waypoint_notes', models.TextField(blank=True)),
                ('completion_notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Route Waypoint',
                'verbose_name_plural': 'Route Waypoints',
                'ordering': ['route', 'sequence_order'],
            },
        ),
        migrations.AddField(
            model_name='importorder',
            name='current_latitude',
            field=models.DecimalField(blank=True, decimal_places=7, help_text='Current latitude coordinate', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='importorder',
            name='current_location_name',
            field=models.CharField(blank=True, help_text='Human-readable current location', max_length=200),
        ),
        migrations.AddField(
            model_name='importorder',
            name='current_longitude',
            field=models.DecimalField(blank=True, decimal_places=7, help_text='Current longitude coordinate', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='importorder',
            name='last_location_update',
            field=models.DateTimeField(blank=True, help_text='When location was last updated', null=True),
        ),
        migrations.AddField(
            model_name='importorder',
            name='tracking_enabled',
            field=models.BooleanField(default=True, help_text='Enable GPS tracking for this order'),
        ),
        migrations.AddIndex(
            model_name='importorder',
            index=models.Index(fields=['current_latitude', 'current_longitude'], name='core_import_current_d61557_idx'),
        ),
        migrations.AddIndex(
            model_name='importorder',
            index=models.Index(fields=['tracking_enabled', 'status'], name='core_import_trackin_1291ad_idx'),
        ),
        migrations.AddField(
            model_name='importorderlocation',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_locations', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='importorderlocation',
            name='import_order',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='locations', to='core.importorder'),
        ),
        migrations.AddField(
            model_name='importorderroute',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_routes', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='importorderroute',
            name='destination_location',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='routes_as_destination', to='core.importorderlocation'),
        ),
        migrations.AddField(
            model_name='importorderroute',
            name='import_order',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='route', to='core.importorder'),
        ),
        migrations.AddField(
            model_name='importorderroute',
            name='origin_location',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='routes_as_origin', to='core.importorderlocation'),
        ),
        migrations.AddField(
            model_name='locationtrackinghistory',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='tracking_entries', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='locationtrackinghistory',
            name='import_order',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tracking_history', to='core.importorder'),
        ),
        migrations.AddField(
            model_name='locationtrackinghistory',
            name='location',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='tracking_entries', to='core.importorderlocation'),
        ),
        migrations.AddField(
            model_name='routewaypoint',
            name='location',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='waypoints', to='core.importorderlocation'),
        ),
        migrations.AddField(
            model_name='routewaypoint',
            name='route',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='waypoints', to='core.importorderroute'),
        ),
        migrations.AddIndex(
            model_name='importorderlocation',
            index=models.Index(fields=['import_order', 'location_type'], name='core_import_import__3e94eb_idx'),
        ),
        migrations.AddIndex(
            model_name='importorderlocation',
            index=models.Index(fields=['import_order', 'is_current_location'], name='core_import_import__a83f67_idx'),
        ),
        migrations.AddIndex(
            model_name='importorderlocation',
            index=models.Index(fields=['latitude', 'longitude'], name='core_import_latitud_cc6694_idx'),
        ),
        migrations.AddIndex(
            model_name='importorderroute',
            index=models.Index(fields=['import_order'], name='core_import_import__eb391c_idx'),
        ),
        migrations.AddIndex(
            model_name='importorderroute',
            index=models.Index(fields=['route_status'], name='core_import_route_s_4619ca_idx'),
        ),
        migrations.AddIndex(
            model_name='locationtrackinghistory',
            index=models.Index(fields=['import_order', '-recorded_at'], name='core_locati_import__8053dd_idx'),
        ),
        migrations.AddIndex(
            model_name='locationtrackinghistory',
            index=models.Index(fields=['status_at_time', '-recorded_at'], name='core_locati_status__eac463_idx'),
        ),
        migrations.AddIndex(
            model_name='locationtrackinghistory',
            index=models.Index(fields=['tracking_source', '-recorded_at'], name='core_locati_trackin_50fc22_idx'),
        ),
        migrations.AddIndex(
            model_name='routewaypoint',
            index=models.Index(fields=['route', 'sequence_order'], name='core_routew_route_i_f68d55_idx'),
        ),
        migrations.AddIndex(
            model_name='routewaypoint',
            index=models.Index(fields=['route', 'is_current'], name='core_routew_route_i_596589_idx'),
        ),
        migrations.AddIndex(
            model_name='routewaypoint',
            index=models.Index(fields=['route', 'is_completed'], name='core_routew_route_i_a0129f_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='routewaypoint',
            unique_together={('route', 'sequence_order')},
        ),
    ]
