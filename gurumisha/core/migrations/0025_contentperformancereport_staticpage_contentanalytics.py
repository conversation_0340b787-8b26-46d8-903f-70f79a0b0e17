# Generated by Django 5.2 on 2025-07-13 16:02

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0024_contentseries_contenttag_alter_blogpost_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContentPerformanceReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('report_type', models.CharField(choices=[('weekly', 'Weekly Report'), ('monthly', 'Monthly Report'), ('quarterly', 'Quarterly Report')], max_length=20)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('total_views', models.PositiveIntegerField(default=0)),
                ('total_unique_views', models.PositiveIntegerField(default=0)),
                ('total_engagement', models.PositiveIntegerField(default=0)),
                ('top_performing_posts', models.JSONField(blank=True, default=list)),
                ('report_data', models.JSONField(blank=True, default=dict)),
                ('is_generated', models.BooleanField(default=False)),
                ('generated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('generated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Performance Report',
                'verbose_name_plural': 'Performance Reports',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StaticPage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(unique=True)),
                ('page_type', models.CharField(choices=[('about', 'About Us'), ('contact', 'Contact Us'), ('privacy', 'Privacy Policy'), ('terms', 'Terms of Service'), ('faq', 'FAQ'), ('help', 'Help Center'), ('custom', 'Custom Page')], default='custom', max_length=20)),
                ('content', models.TextField()),
                ('excerpt', models.TextField(blank=True, help_text='Brief description of the page')),
                ('meta_title', models.CharField(blank=True, help_text='SEO title (60 chars max)', max_length=60)),
                ('meta_description', models.CharField(blank=True, help_text='SEO description (160 chars max)', max_length=160)),
                ('meta_keywords', models.CharField(blank=True, max_length=200)),
                ('featured_image', models.ImageField(blank=True, upload_to='static_pages/')),
                ('featured_image_alt', models.CharField(blank=True, max_length=200)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('published', 'Published'), ('archived', 'Archived')], default='draft', max_length=20)),
                ('is_featured', models.BooleanField(default=False, help_text='Show in featured sections')),
                ('show_in_menu', models.BooleanField(default=False, help_text='Show in navigation menu')),
                ('menu_order', models.PositiveIntegerField(default=0, help_text='Order in navigation menu')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('published_at', models.DateTimeField(blank=True, null=True)),
                ('views_count', models.PositiveIntegerField(default=0)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='static_pages', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Static Page',
                'verbose_name_plural': 'Static Pages',
                'ordering': ['menu_order', 'title'],
            },
        ),
        migrations.CreateModel(
            name='ContentAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('views', models.PositiveIntegerField(default=0)),
                ('unique_views', models.PositiveIntegerField(default=0)),
                ('likes', models.PositiveIntegerField(default=0)),
                ('shares', models.PositiveIntegerField(default=0)),
                ('comments', models.PositiveIntegerField(default=0)),
                ('bookmarks', models.PositiveIntegerField(default=0)),
                ('avg_time_on_page', models.DurationField(blank=True, null=True)),
                ('bounce_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('direct_traffic', models.PositiveIntegerField(default=0)),
                ('search_traffic', models.PositiveIntegerField(default=0)),
                ('social_traffic', models.PositiveIntegerField(default=0)),
                ('referral_traffic', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to='core.blogpost')),
            ],
            options={
                'verbose_name': 'Content Analytics',
                'verbose_name_plural': 'Content Analytics',
                'ordering': ['-date'],
                'unique_together': {('post', 'date')},
            },
        ),
    ]
