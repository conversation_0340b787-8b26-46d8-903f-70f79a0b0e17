# Generated by Django 5.2 on 2025-07-13 15:26

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0023_alter_sparepartcategory_name_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContentSeries',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(unique=True)),
                ('description', models.TextField()),
                ('featured_image', models.ImageField(blank=True, upload_to='content/series/')),
                ('is_active', models.BooleanField(default=True)),
                ('sort_order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Content Series',
                'ordering': ['sort_order', 'title'],
            },
        ),
        migrations.CreateModel(
            name='ContentTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('slug', models.SlugField(unique=True)),
                ('description', models.TextField(blank=True)),
                ('color', models.CharField(default='#6B7280', help_text='Hex color code for tag', max_length=7)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.AlterModelOptions(
            name='blogpost',
            options={'ordering': ['-published_at', '-created_at'], 'verbose_name': 'Content Post', 'verbose_name_plural': 'Content Posts'},
        ),
        migrations.AddField(
            model_name='blogpost',
            name='content_type',
            field=models.CharField(choices=[('article', 'Article'), ('guide', 'Guide'), ('infographic', 'Infographic'), ('opinion', 'Opinion'), ('news', 'News'), ('review', 'Review')], default='article', max_length=20),
        ),
        migrations.AddField(
            model_name='blogpost',
            name='difficulty_level',
            field=models.CharField(blank=True, choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced')], max_length=20),
        ),
        migrations.AddField(
            model_name='blogpost',
            name='estimated_read_time',
            field=models.PositiveIntegerField(default=5, help_text='Estimated reading time in minutes'),
        ),
        migrations.AddField(
            model_name='blogpost',
            name='featured_image_alt',
            field=models.CharField(blank=True, help_text='Alt text for featured image', max_length=200),
        ),
        migrations.AddField(
            model_name='blogpost',
            name='is_featured',
            field=models.BooleanField(default=False, help_text='Show in featured content sections'),
        ),
        migrations.AddField(
            model_name='blogpost',
            name='likes_count',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='blogpost',
            name='shares_count',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='blogpost',
            name='video_url',
            field=models.URLField(blank=True, help_text='YouTube or Vimeo URL'),
        ),
        migrations.AddField(
            model_name='blogpost',
            name='views_count',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='blogpost',
            name='excerpt',
            field=models.TextField(blank=True, help_text='Brief summary of the content'),
        ),
        migrations.AlterField(
            model_name='blogpost',
            name='featured_image',
            field=models.ImageField(blank=True, upload_to='content/featured/'),
        ),
        migrations.CreateModel(
            name='ContentCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('slug', models.SlugField(unique=True)),
                ('description', models.TextField(blank=True)),
                ('icon', models.CharField(blank=True, help_text='Font Awesome icon class', max_length=50)),
                ('color', models.CharField(default='#3B82F6', help_text='Hex color code for category', max_length=7)),
                ('is_active', models.BooleanField(default=True)),
                ('sort_order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subcategories', to='core.contentcategory')),
            ],
            options={
                'verbose_name_plural': 'Content Categories',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.AddField(
            model_name='blogpost',
            name='category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='posts', to='core.contentcategory'),
        ),
        migrations.CreateModel(
            name='ContentComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField()),
                ('is_approved', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='core.contentcomment')),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='core.blogpost')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='blogpost',
            name='tags',
            field=models.ManyToManyField(blank=True, related_name='posts', to='core.contenttag'),
        ),
        migrations.CreateModel(
            name='ContentView',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True)),
                ('referrer', models.URLField(blank=True)),
                ('viewed_at', models.DateTimeField(auto_now_add=True)),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='content_views', to='core.blogpost')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-viewed_at'],
            },
        ),
        migrations.CreateModel(
            name='ContentBookmark',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bookmarks', to='core.blogpost')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bookmarks', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('user', 'post')},
            },
        ),
        migrations.CreateModel(
            name='ContentLike',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='content_likes', to='core.blogpost')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('post', 'user')},
            },
        ),
        migrations.CreateModel(
            name='ContentSeriesItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='series_items', to='core.blogpost')),
                ('series', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='core.contentseries')),
            ],
            options={
                'ordering': ['order'],
                'unique_together': {('series', 'post')},
            },
        ),
    ]
