# Generated by Django 5.2 on 2025-07-12 19:29

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0020_profile_analytics_system'),
    ]

    operations = [
        migrations.CreateModel(
            name='VehicleCondition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, help_text='Detailed description of this condition')),
                ('display_order', models.PositiveIntegerField(default=0, help_text='Order for display in forms')),
                ('is_active', models.BooleanField(default=True)),
                ('color_code', models.CharField(blank=True, help_text='Hex color code for UI display', max_length=7)),
                ('icon_class', models.Char<PERSON>ield(blank=True, help_text='CSS icon class for display', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Vehicle Condition',
                'verbose_name_plural': 'Vehicle Conditions',
                'ordering': ['display_order', 'name'],
            },
        ),
        migrations.AlterModelOptions(
            name='carbrand',
            options={'ordering': ['display_order', 'name']},
        ),
        migrations.AddField(
            model_name='carbrand',
            name='country_of_origin',
            field=models.CharField(blank=True, help_text='Country where brand originated', max_length=100),
        ),
        migrations.AddField(
            model_name='carbrand',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.AddField(
            model_name='carbrand',
            name='description',
            field=models.TextField(blank=True, help_text='Brand description and history'),
        ),
        migrations.AddField(
            model_name='carbrand',
            name='display_order',
            field=models.PositiveIntegerField(default=0, help_text='Order for display in lists'),
        ),
        migrations.AddField(
            model_name='carbrand',
            name='is_premium',
            field=models.BooleanField(default=False, help_text='Mark as premium/luxury brand'),
        ),
        migrations.AddField(
            model_name='carbrand',
            name='logo_url',
            field=models.URLField(blank=True, help_text='Alternative logo URL if not uploaded'),
        ),
        migrations.AddField(
            model_name='carbrand',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.AddField(
            model_name='carbrand',
            name='website',
            field=models.URLField(blank=True, help_text='Official brand website'),
        ),
        migrations.AddField(
            model_name='carmodel',
            name='body_type',
            field=models.CharField(blank=True, choices=[('sedan', 'Sedan'), ('suv', 'SUV'), ('hatchback', 'Hatchback'), ('coupe', 'Coupe'), ('convertible', 'Convertible'), ('wagon', 'Wagon'), ('pickup', 'Pickup Truck'), ('van', 'Van'), ('crossover', 'Crossover'), ('sports', 'Sports Car'), ('luxury', 'Luxury'), ('compact', 'Compact'), ('other', 'Other')], max_length=20),
        ),
        migrations.AddField(
            model_name='carmodel',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.AddField(
            model_name='carmodel',
            name='description',
            field=models.TextField(blank=True, help_text='Model description and key features'),
        ),
        migrations.AddField(
            model_name='carmodel',
            name='engine_options',
            field=models.TextField(blank=True, help_text='Available engine options (comma-separated)'),
        ),
        migrations.AddField(
            model_name='carmodel',
            name='is_popular',
            field=models.BooleanField(default=False, help_text='Mark as popular model for featured display'),
        ),
        migrations.AddField(
            model_name='carmodel',
            name='model_year_end',
            field=models.PositiveIntegerField(blank=True, help_text='Last year this model was produced (blank if still in production)', null=True),
        ),
        migrations.AddField(
            model_name='carmodel',
            name='model_year_start',
            field=models.PositiveIntegerField(blank=True, help_text='First year this model was produced', null=True),
        ),
        migrations.AddField(
            model_name='carmodel',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.AlterField(
            model_name='car',
            name='condition',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.vehiclecondition'),
        ),
    ]
