<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome Popup Test - Guru<PERSON>ha</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary-red': '#DC2626',
                        'primary-black': '#1F2937',
                        'primary-blue': '#1E3A8A',
                        'primary-white': '#FFFFFF',
                        'accent-red': '#EF4444',
                        'accent-gray': '#F3F4F6',
                        'text-dark': '#111827',
                        'text-light': '#6B7280',
                    },
                    fontFamily: {
                        'montserrat': ['Montserrat', 'sans-serif'],
                        'raleway': ['Raleway', 'sans-serif'],
                        'inter': ['Inter', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700;800&family=Raleway:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-accent-gray font-inter text-text-dark antialiased">
    
    <!-- Test Page Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <div class="h-8 w-8 bg-primary-red rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-sm">G</span>
                    </div>
                    <h1 class="ml-3 text-xl font-bold text-text-dark font-montserrat">Welcome Popup Test</h1>
                </div>
                <div class="flex space-x-4">
                    <button onclick="WelcomePopup.forceShow()" 
                            class="bg-primary-red text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                        <i class="fas fa-redo mr-2"></i>Force Show Popup
                    </button>
                    <button onclick="WelcomePopup.reset()" 
                            class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fas fa-trash mr-2"></i>Reset Data
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Hero Section (simulating homepage) -->
        <section class="main-banner bg-gradient-to-br from-primary-black via-primary-blue to-primary-red rounded-2xl text-white p-8 mb-8">
            <div class="hero-content text-center">
                <h2 class="text-4xl font-bold font-montserrat mb-4">Find Your Perfect Vehicle</h2>
                <p class="text-xl text-gray-200 mb-6 font-raleway">Discover premium vehicles from trusted dealers</p>
                
                <!-- Car Search Form -->
                <div class="car-search-form bg-white/10 backdrop-blur-md rounded-xl p-6 max-w-2xl mx-auto">
                    <h3 class="text-lg font-semibold mb-4 font-montserrat">Search for Cars</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <select class="px-4 py-2 rounded-lg border border-gray-300 text-gray-800">
                            <option>Select Brand</option>
                            <option>Toyota</option>
                            <option>Honda</option>
                            <option>Nissan</option>
                        </select>
                        <select class="px-4 py-2 rounded-lg border border-gray-300 text-gray-800">
                            <option>Select Model</option>
                            <option>Camry</option>
                            <option>Civic</option>
                            <option>Altima</option>
                        </select>
                        <button class="bg-primary-red text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors font-semibold">
                            <i class="fas fa-search mr-2"></i>Search
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Test Controls -->
        <section class="bg-white rounded-xl shadow-sm border p-6 mb-8">
            <h3 class="text-xl font-bold font-montserrat mb-4">Test Controls</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <button onclick="testShowPopup()" 
                        class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Show Popup
                </button>
                <button onclick="testSessionStorage()" 
                        class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                    Test Session Storage
                </button>
                <button onclick="testLocalStorage()" 
                        class="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors">
                    Test Local Storage
                </button>
                <button onclick="showAnalytics()" 
                        class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                    Show Analytics
                </button>
            </div>
        </section>

        <!-- Analytics Display -->
        <section class="bg-white rounded-xl shadow-sm border p-6">
            <h3 class="text-xl font-bold font-montserrat mb-4">Analytics & Debug Info</h3>
            <div id="analytics-display" class="bg-gray-50 rounded-lg p-4 font-mono text-sm">
                <p>Click "Show Analytics" to view popup analytics data</p>
            </div>
        </section>

    </main>

    <!-- Welcome Popup Component -->
    <div id="welcome-popup" 
         class="fixed inset-0 z-50 overflow-y-auto hidden"
         x-data="welcomePopup()"
         x-init="init()"
         x-show="show"
         x-transition:enter="ease-out duration-500"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         role="dialog"
         aria-modal="true"
         aria-labelledby="welcome-title"
         aria-describedby="welcome-description">
        
        <!-- Backdrop -->
        <div class="fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity"
             @click="closePopup()"
             aria-hidden="true"></div>
        
        <!-- Popup Container -->
        <div class="flex min-h-full items-center justify-center p-4 text-center sm:p-0">
            
            <!-- Popup Panel -->
            <div class="relative transform overflow-hidden rounded-2xl bg-white/95 backdrop-blur-md text-left shadow-2xl transition-all sm:my-8 sm:w-full sm:max-w-lg border border-white/20"
                 x-show="show"
                 x-transition:enter="ease-out duration-500"
                 x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave="ease-in duration-300"
                 x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
                
                <!-- Close Button -->
                <button type="button" 
                        class="absolute top-4 right-4 z-10 rounded-full bg-white/80 p-2 text-gray-400 hover:text-gray-600 hover:bg-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-red focus:ring-offset-2"
                        @click="closePopup()"
                        aria-label="Close welcome popup">
                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
                
                <!-- Header -->
                <div class="relative bg-gradient-to-br from-primary-red via-red-600 to-black px-6 pt-8 pb-6 text-center">
                    <div class="relative z-10 mb-4">
                        <div class="h-16 w-16 mx-auto bg-white rounded-full flex items-center justify-center">
                            <span class="text-2xl font-bold text-primary-red">G</span>
                        </div>
                    </div>
                    
                    <h2 id="welcome-title" 
                        class="text-2xl sm:text-3xl font-bold text-white font-montserrat mb-2">
                        Welcome to Gurumisha!
                    </h2>
                    
                    <p class="text-red-100 text-sm font-raleway">
                        Kenya's Premier Automotive Marketplace
                    </p>
                </div>
                
                <!-- Content -->
                <div class="px-6 py-6">
                    <div id="welcome-description" class="mb-6">
                        <p class="text-gray-700 text-base leading-relaxed font-inter mb-4">
                            Discover your perfect vehicle with Kenya's most trusted automotive platform. Whether you're looking to buy, sell, or import, we've got you covered.
                        </p>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-3 mb-4">
                        <button type="button"
                                class="flex-1 bg-gradient-to-r from-primary-red to-red-600 text-white px-6 py-3 rounded-lg font-semibold font-raleway transition-all duration-300 hover:from-red-600 hover:to-red-700 hover:shadow-lg transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-red focus:ring-offset-2"
                                @click="getStarted()">
                            Get Started
                        </button>
                        
                        <button type="button"
                                class="flex-1 bg-white border-2 border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold font-raleway transition-all duration-300 hover:border-primary-red hover:text-primary-red hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary-red focus:ring-offset-2"
                                @click="browseCars()">
                            Browse Cars
                        </button>
                    </div>
                    
                    <!-- Don't show again -->
                    <div class="text-center">
                        <label class="inline-flex items-center cursor-pointer">
                            <input type="checkbox" 
                                   x-model="dontShowAgain"
                                   class="rounded border-gray-300 text-primary-red focus:ring-primary-red focus:ring-offset-0">
                            <span class="ml-2 text-sm text-gray-600 font-inter">Don't show this again</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test JavaScript -->
    <script>
        // Include the welcome popup JavaScript inline for testing
        // (In production, this would be loaded from static/js/welcome-popup.js)
        
        // Simple welcome popup function for testing
        function welcomePopup() {
            return {
                show: false,
                dontShowAgain: false,
                
                init() {
                    // For testing, always show after a delay
                    setTimeout(() => {
                        this.show = true;
                        document.body.style.overflow = 'hidden';
                    }, 1000);
                },
                
                closePopup() {
                    this.show = false;
                    document.body.style.overflow = '';
                    
                    if (this.dontShowAgain) {
                        localStorage.setItem('gurumisha_welcome_dont_show', 'true');
                        alert('Preference saved: Won\'t show again');
                    }
                    
                    setTimeout(() => {
                        const popup = document.getElementById('welcome-popup');
                        if (popup) {
                            popup.classList.add('hidden');
                        }
                    }, 300);
                },
                
                getStarted() {
                    this.closePopup();
                    setTimeout(() => {
                        document.querySelector('.car-search-form').scrollIntoView({ 
                            behavior: 'smooth',
                            block: 'center'
                        });
                    }, 400);
                },
                
                browseCars() {
                    this.closePopup();
                    alert('Would navigate to car listings page');
                }
            }
        }
        
        // Test functions
        function testShowPopup() {
            const popup = document.getElementById('welcome-popup');
            popup.classList.remove('hidden');
            // Trigger Alpine.js to show
            popup._x_dataStack[0].show = true;
        }
        
        function testSessionStorage() {
            const data = {
                session: sessionStorage.getItem('gurumisha_welcome_seen'),
                local: localStorage.getItem('gurumisha_welcome_dont_show')
            };
            alert('Storage Data:\n' + JSON.stringify(data, null, 2));
        }
        
        function testLocalStorage() {
            localStorage.removeItem('gurumisha_welcome_dont_show');
            sessionStorage.removeItem('gurumisha_welcome_seen');
            alert('Storage cleared! Refresh page to see popup again.');
        }
        
        function showAnalytics() {
            const analytics = {
                sessionStorage: sessionStorage.getItem('gurumisha_welcome_seen'),
                localStorage: localStorage.getItem('gurumisha_welcome_dont_show'),
                userAgent: navigator.userAgent,
                viewport: window.innerWidth + 'x' + window.innerHeight,
                timestamp: new Date().toISOString()
            };
            
            document.getElementById('analytics-display').innerHTML = 
                '<pre>' + JSON.stringify(analytics, null, 2) + '</pre>';
        }
        
        // Mock WelcomePopup object for testing
        window.WelcomePopup = {
            forceShow() {
                localStorage.removeItem('gurumisha_welcome_dont_show');
                sessionStorage.removeItem('gurumisha_welcome_seen');
                location.reload();
            },
            
            reset() {
                localStorage.removeItem('gurumisha_welcome_dont_show');
                sessionStorage.removeItem('gurumisha_welcome_seen');
                alert('Welcome popup data reset!');
            }
        };
    </script>

</body>
</html>
