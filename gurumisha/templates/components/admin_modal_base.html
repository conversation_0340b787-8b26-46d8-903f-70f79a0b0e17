{% load static %}

<!-- Base Admin Modal Component -->
<!-- Usage: {% include 'components/admin_modal_base.html' with modal_id='unique-id' modal_title='Title' modal_size='lg' %} -->

<div class="fixed inset-0 z-[1500] overflow-y-auto"
     id="{{ modal_id|default:'admin-modal' }}"
     x-data="{ show: false }"
     x-init="show = true"
     x-show="show"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">

    <!-- Modal Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity z-[1400]"
         @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)"></div>
    
    <!-- Modal Container -->
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <!-- Centering trick -->
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        
        <!-- Modal Panel -->
        <div class="inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle {% if modal_size == 'sm' %}sm:max-w-sm{% elif modal_size == 'lg' %}sm:max-w-4xl{% elif modal_size == 'xl' %}sm:max-w-6xl{% else %}sm:max-w-lg{% endif %} sm:w-full"
             x-show="show"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
            
            <!-- Modal Header -->
            <div class="bg-white px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        {% if modal_icon %}
                        <div class="w-10 h-10 bg-gradient-to-br from-harrier-red to-harrier-dark rounded-lg flex items-center justify-center mr-3">
                            <i class="{{ modal_icon }} text-white"></i>
                        </div>
                        {% endif %}
                        <div>
                            <h3 class="text-lg font-bold text-harrier-dark font-montserrat">
                                {{ modal_title|default:'Modal Title' }}
                            </h3>
                            {% if modal_subtitle %}
                            <p class="text-sm text-gray-600 font-raleway">{{ modal_subtitle }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Close Button -->
                    <button type="button" 
                            class="text-gray-400 hover:text-gray-600 transition-colors"
                            @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                        <span class="sr-only">Close</span>
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            
            <!-- Modal Body -->
            <div class="bg-white px-6 py-4 max-h-96 overflow-y-auto">
                {% block modal_content %}
                    <p class="text-gray-600">Modal content goes here.</p>
                {% endblock %}
            </div>
            
            <!-- Modal Footer -->
            {% if not hide_footer %}
            <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3">
                {% block modal_footer %}
                    <button type="button" 
                            class="btn-admin-secondary"
                            @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                        Cancel
                    </button>
                    <button type="button" class="btn-admin-primary">
                        Confirm
                    </button>
                {% endblock %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
    /* Modal Animation Enhancements */
    .modal-enter {
        animation: modalEnter 0.3s ease-out;
    }
    
    .modal-leave {
        animation: modalLeave 0.2s ease-in;
    }
    
    @keyframes modalEnter {
        from {
            opacity: 0;
            transform: scale(0.95) translateY(-10px);
        }
        to {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
    }
    
    @keyframes modalLeave {
        from {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
        to {
            opacity: 0;
            transform: scale(0.95) translateY(-10px);
        }
    }
    
    /* Backdrop blur effect */
    .modal-backdrop {
        backdrop-filter: blur(4px);
        -webkit-backdrop-filter: blur(4px);
    }
    
    /* Enhanced scrollbar for modal body */
    .modal-body::-webkit-scrollbar {
        width: 6px;
    }
    
    .modal-body::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }
    
    .modal-body::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }
    
    .modal-body::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }
</style>
