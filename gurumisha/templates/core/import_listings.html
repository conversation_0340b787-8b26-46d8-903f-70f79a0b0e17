{% extends 'base.html' %}
{% load static %}

{% block title %}Car Import Services - Gurumisha{% endblock %}

{% block content %}
<!-- Enhanced Hero Section with Background Image -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden">
    <!-- Background Image with Parallax Effect -->
    <div class="absolute inset-0 bg-cover bg-center bg-no-repeat transform scale-105"
         style="background-image: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.6)), url('{% static 'images/banner-image-1-1920x500.jpg' %}');">
    </div>

    <!-- Overlay Gradients -->
    <div class="absolute inset-0 bg-gradient-to-r from-harrier-dark via-harrier-dark/80 to-transparent"></div>
    <div class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/30"></div>

    <!-- Animated Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-harrier-red/10 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
    </div>

    <!-- Content -->
    <div class="relative z-10 w-full py-20 lg:py-32">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <!-- Left Content -->
                <div class="text-left space-y-8">
                    <!-- Badge -->
                    <div class="inline-flex items-center px-4 py-2 bg-harrier-red/20 border border-harrier-red/30 rounded-full text-harrier-red text-sm font-medium backdrop-blur-sm">
                        <i class="fas fa-star mr-2"></i>
                        Trusted Import Specialists Since 2020
                    </div>

                    <!-- Main Heading -->
                    <h1 class="text-4xl md:text-6xl lg:text-7xl font-heading font-bold text-white leading-tight">
                        IMPORT YOUR
                        <span class="block text-harrier-red bg-gradient-to-r from-harrier-red to-red-400 bg-clip-text text-transparent">
                            DREAM CAR
                        </span>
                        <span class="block text-3xl md:text-4xl lg:text-5xl text-gray-300 font-normal mt-2">
                            From Anywhere
                        </span>
                    </h1>

                    <!-- Description -->
                    <p class="text-xl md:text-2xl text-gray-300 leading-relaxed max-w-2xl">
                        Access global automotive markets and import high-quality vehicles from
                        <span class="text-white font-semibold">Japan, Germany, UK, USA</span> and more.
                        Professional handling from auction to your doorstep.
                    </p>

                    <!-- Stats -->
                    <div class="grid grid-cols-3 gap-6 py-6">
                        <div class="text-center lg:text-left">
                            <div class="text-3xl md:text-4xl font-bold text-harrier-red">500+</div>
                            <div class="text-gray-400 text-sm">Cars Imported</div>
                        </div>
                        <div class="text-center lg:text-left">
                            <div class="text-3xl md:text-4xl font-bold text-harrier-red">15+</div>
                            <div class="text-gray-400 text-sm">Countries</div>
                        </div>
                        <div class="text-center lg:text-left">
                            <div class="text-3xl md:text-4xl font-bold text-harrier-red">98%</div>
                            <div class="text-gray-400 text-sm">Success Rate</div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="{% url 'core:import_request' %}"
                           class="group btn-harrier-primary text-lg px-8 py-4 inline-flex items-center justify-center transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                            <i class="fas fa-paper-plane mr-3 group-hover:translate-x-1 transition-transform"></i>
                            START IMPORT REQUEST
                        </a>
                        <a href="{% url 'core:import_order_tracking' %}"
                           class="group btn-harrier-secondary text-lg px-8 py-4 inline-flex items-center justify-center transform hover:scale-105 transition-all duration-300">
                            <i class="fas fa-search mr-3 group-hover:rotate-12 transition-transform"></i>
                            TRACK YOUR ORDER
                        </a>
                    </div>

                    <!-- Quick Search -->
                    <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
                        <h3 class="text-white font-semibold mb-4 flex items-center">
                            <i class="fas fa-bolt text-harrier-red mr-2"></i>
                            Quick Chassis Search
                        </h3>
                        <form action="{% url 'core:chassis_number_search' %}" method="get" class="flex gap-3">
                            <input type="text"
                                   name="chassis_number"
                                   placeholder="Enter chassis number..."
                                   class="flex-1 px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-gray-300 focus:ring-2 focus:ring-harrier-red focus:border-transparent backdrop-blur-sm">
                            <button type="submit"
                                    class="px-6 py-3 bg-harrier-red hover:bg-red-600 text-white rounded-lg font-medium transition-colors">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Right Content - Visual Elements -->
                <div class="relative lg:block hidden">
                    <!-- Floating Cards -->
                    <div class="relative">
                        <!-- Main Car Card -->
                        <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 transform rotate-3 hover:rotate-0 transition-transform duration-500">
                            <div class="flex items-center space-x-4 mb-4">
                                <div class="w-12 h-12 bg-harrier-red rounded-full flex items-center justify-center">
                                    <i class="fas fa-car text-white text-xl"></i>
                                </div>
                                <div>
                                    <h4 class="text-white font-semibold">2023 Toyota Prius</h4>
                                    <p class="text-gray-300 text-sm">From Japan</p>
                                </div>
                            </div>
                            <div class="space-y-2">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-300">Status:</span>
                                    <span class="text-green-400 font-medium">In Transit</span>
                                </div>
                                <div class="w-full bg-gray-700 rounded-full h-2">
                                    <div class="bg-harrier-red h-2 rounded-full w-3/4"></div>
                                </div>
                                <div class="text-xs text-gray-400">75% Complete</div>
                            </div>
                        </div>

                        <!-- Floating Stats -->
                        <div class="absolute -top-6 -right-6 bg-harrier-red/90 backdrop-blur-lg rounded-xl p-4 border border-red-400/30 animate-bounce">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-white">7-14</div>
                                <div class="text-xs text-red-100">Days Delivery</div>
                            </div>
                        </div>

                        <!-- Process Steps -->
                        <div class="absolute -bottom-8 -left-8 bg-white/10 backdrop-blur-lg rounded-xl p-4 border border-white/20">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                                <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                                <div class="w-3 h-3 bg-harrier-red rounded-full animate-pulse"></div>
                                <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                            </div>
                            <div class="text-xs text-white mt-2">Import Progress</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <div class="flex flex-col items-center space-y-2">
            <span class="text-sm text-gray-300">Scroll to explore</span>
            <i class="fas fa-chevron-down text-harrier-red"></i>
        </div>
    </div>
</section>

<!-- Enhanced Popular Import Destinations with Glassmorphism -->
<section class="py-16 relative overflow-hidden">
    <!-- Background with Red-to-Black Gradient -->
    <div class="absolute inset-0 bg-gradient-to-br from-harrier-red via-red-800 to-harrier-dark"></div>
    <div class="absolute inset-0 bg-black bg-opacity-20"></div>

    <!-- Animated Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-white bg-opacity-5 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-white bg-opacity-5 rounded-full blur-3xl animate-pulse" style="animation-delay: 1s;"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <!-- Enhanced Section Header -->
        <div class="text-center mb-12 animate-entrance">
            <div class="inline-flex items-center justify-center w-14 h-14 bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-full mb-4 border border-white border-opacity-20">
                <i class="fas fa-globe-americas text-white text-xl"></i>
            </div>
            <h2 class="text-3xl md:text-4xl font-montserrat font-bold text-white mb-4 tracking-tight">
                POPULAR IMPORT DESTINATIONS
            </h2>
            <p class="text-lg text-white text-opacity-90 max-w-4xl mx-auto font-raleway leading-relaxed">
                We have established partnerships with trusted dealers and auction houses worldwide to bring you the best vehicles
            </p>
        </div>

        <!-- Enhanced Destination Cards Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {% for import in popular_imports %}
                <div class="destination-card group cursor-pointer" data-country="{{ import.country }}">
                    <!-- Glassmorphism Card Container -->
                    <div class="relative bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-2xl overflow-hidden border border-white border-opacity-20 shadow-2xl transition-all duration-500 hover:transform hover:scale-105 hover:bg-opacity-15">

                        <!-- Image Section with Overlay -->
                        <div class="relative h-56 overflow-hidden">
                            <!-- Country Flag/Image Background -->
                            <div class="absolute inset-0 bg-gradient-to-br from-white from-opacity-10 to-black to-opacity-30"></div>

                            <!-- Country Icon and Name -->
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="text-center text-white transform transition-transform duration-500 group-hover:scale-110">
                                    {% if import.country == 'Japan' %}
                                        <i class="fas fa-torii-gate text-5xl mb-4 drop-shadow-lg"></i>
                                    {% elif import.country == 'Germany' %}
                                        <i class="fas fa-car text-5xl mb-4 drop-shadow-lg"></i>
                                    {% elif import.country == 'UK' %}
                                        <i class="fas fa-crown text-5xl mb-4 drop-shadow-lg"></i>
                                    {% elif import.country == 'USA' %}
                                        <i class="fas fa-flag-usa text-5xl mb-4 drop-shadow-lg"></i>
                                    {% else %}
                                        <i class="fas fa-flag text-5xl mb-4 drop-shadow-lg"></i>
                                    {% endif %}
                                    <h3 class="text-xl font-montserrat font-bold tracking-wide drop-shadow-lg">{{ import.country }}</h3>
                                </div>
                            </div>

                            <!-- Hover Overlay -->
                            <div class="absolute inset-0 bg-harrier-red bg-opacity-0 transition-all duration-500 group-hover:bg-opacity-20"></div>
                        </div>

                        <!-- Content Section -->
                        <div class="p-5 relative">
                            <p class="text-white text-opacity-90 mb-6 font-raleway leading-relaxed">{{ import.description }}</p>

                            <!-- Action Section -->
                            <div class="flex items-center justify-between">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-white bg-opacity-20 text-white border border-white border-opacity-30">
                                    <i class="fas fa-star text-yellow-400 mr-1"></i>
                                    Popular Choice
                                </span>
                                <a href="{% url 'core:import_request' %}"
                                   class="inline-flex items-center justify-center w-10 h-10 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full text-white transition-all duration-300 hover:transform hover:scale-110 border border-white border-opacity-30"
                                   aria-label="Start import request for {{ import.country }}">
                                    <i class="fas fa-arrow-right text-sm"></i>
                                </a>
                            </div>
                        </div>

                        <!-- Glassmorphism Border Glow Effect -->
                        <div class="absolute inset-0 rounded-2xl border-2 border-transparent bg-gradient-to-r from-white via-transparent to-white bg-clip-border opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- Enhanced CTA Button -->
        <div class="text-center mt-12">
            <a href="{% url 'core:import_request' %}"
               class="inline-flex items-center px-8 py-4 bg-white bg-opacity-10 hover:bg-opacity-20 backdrop-filter backdrop-blur-lg rounded-xl text-white font-montserrat font-semibold text-lg transition-all duration-500 hover:transform hover:scale-105 border border-white border-opacity-30 shadow-2xl group"
               aria-label="Explore all import destinations">
                <i class="fas fa-compass mr-3 text-xl group-hover:rotate-12 transition-transform duration-300"></i>
                EXPLORE ALL DESTINATIONS
                <i class="fas fa-arrow-right ml-3 group-hover:translate-x-1 transition-transform duration-300"></i>
            </a>
        </div>
    </div>
</section>

<!-- Enhanced Import Process with Glassmorphism Timeline -->
<section id="process" class="py-16 relative overflow-hidden bg-gradient-to-br from-gray-50 to-white">
    <!-- Subtle Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(220,38,38,0.3) 1px, transparent 0); background-size: 40px 40px;"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <!-- Enhanced Section Header -->
        <div class="text-center mb-16 animate-entrance">
            <div class="inline-flex items-center justify-center w-14 h-14 bg-gradient-to-br from-harrier-red to-harrier-dark rounded-full mb-4 shadow-xl">
                <i class="fas fa-route text-white text-xl"></i>
            </div>
            <h2 class="text-3xl md:text-4xl font-montserrat font-bold text-harrier-dark mb-4 tracking-tight">
                HOW IMPORT WORKS
            </h2>
            <p class="text-lg text-gray-600 max-w-4xl mx-auto font-raleway leading-relaxed">
                Our streamlined 5-step process makes importing your dream car simple, transparent, and stress-free
            </p>
        </div>

        <!-- Enhanced Process Timeline -->
        <div class="max-w-7xl mx-auto">
            <!-- Desktop Timeline -->
            <div class="hidden lg:block relative">
                <!-- Timeline Line -->
                <div class="absolute top-24 left-0 right-0 h-1 bg-gradient-to-r from-harrier-red via-red-600 to-harrier-dark rounded-full shadow-lg"></div>

                <div class="grid grid-cols-5 gap-8">
                    {% for step in import_process %}
                        <div class="process-step text-center group" data-step="{{ step.step }}">
                            <!-- Step Circle with Glassmorphism -->
                            <div class="relative mb-6">
                                <div class="w-20 h-20 bg-white bg-opacity-90 backdrop-filter backdrop-blur-lg border-4 border-harrier-red rounded-full flex items-center justify-center mx-auto shadow-2xl transition-all duration-500 group-hover:scale-110 group-hover:shadow-3xl relative z-10">
                                    <!-- Step Icon -->
                                    <div class="text-center">
                                        {% if step.step == 1 %}
                                            <i class="fas fa-paper-plane text-harrier-red text-xl mb-1"></i>
                                        {% elif step.step == 2 %}
                                            <i class="fas fa-calculator text-harrier-red text-xl mb-1"></i>
                                        {% elif step.step == 3 %}
                                            <i class="fas fa-handshake text-harrier-red text-xl mb-1"></i>
                                        {% elif step.step == 4 %}
                                            <i class="fas fa-ship text-harrier-red text-xl mb-1"></i>
                                        {% elif step.step == 5 %}
                                            <i class="fas fa-home text-harrier-red text-xl mb-1"></i>
                                        {% endif %}
                                        <div class="text-xs font-bold text-harrier-dark">{{ step.step }}</div>
                                    </div>
                                </div>

                                <!-- Pulse Animation -->
                                <div class="absolute inset-0 w-20 h-20 bg-harrier-red rounded-full mx-auto opacity-0 group-hover:opacity-20 group-hover:animate-ping transition-opacity duration-300"></div>
                            </div>

                            <!-- Step Content Card -->
                            <div class="bg-white bg-opacity-80 backdrop-filter backdrop-blur-lg rounded-2xl p-5 shadow-xl border border-white border-opacity-50 transition-all duration-500 group-hover:transform group-hover:scale-105 group-hover:shadow-2xl">
                                <h3 class="text-lg font-montserrat font-bold text-harrier-dark mb-2 group-hover:text-harrier-red transition-colors duration-300">
                                    {{ step.title }}
                                </h3>
                                <p class="text-gray-600 font-raleway leading-relaxed">{{ step.description }}</p>

                                <!-- Step Number Badge -->
                                <div class="absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-br from-harrier-red to-harrier-dark rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg">
                                    {{ step.step }}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Mobile/Tablet Vertical Timeline -->
            <div class="lg:hidden space-y-8">
                {% for step in import_process %}
                    <div class="flex items-start space-x-6 group">
                        <!-- Step Circle -->
                        <div class="flex-shrink-0 relative">
                            <div class="w-14 h-14 bg-white bg-opacity-90 backdrop-filter backdrop-blur-lg border-3 border-harrier-red rounded-full flex items-center justify-center shadow-xl transition-all duration-300 group-hover:scale-110">
                                {% if step.step == 1 %}
                                    <i class="fas fa-paper-plane text-harrier-red text-lg"></i>
                                {% elif step.step == 2 %}
                                    <i class="fas fa-calculator text-harrier-red text-lg"></i>
                                {% elif step.step == 3 %}
                                    <i class="fas fa-handshake text-harrier-red text-lg"></i>
                                {% elif step.step == 4 %}
                                    <i class="fas fa-ship text-harrier-red text-lg"></i>
                                {% elif step.step == 5 %}
                                    <i class="fas fa-home text-harrier-red text-lg"></i>
                                {% endif %}
                            </div>

                            <!-- Connecting Line -->
                            {% if not forloop.last %}
                                <div class="absolute top-16 left-1/2 transform -translate-x-1/2 w-1 h-16 bg-gradient-to-b from-harrier-red to-red-400 rounded-full"></div>
                            {% endif %}
                        </div>

                        <!-- Step Content -->
                        <div class="flex-1 bg-white bg-opacity-80 backdrop-filter backdrop-blur-lg rounded-xl p-5 shadow-lg border border-white border-opacity-50 transition-all duration-300 group-hover:shadow-xl">
                            <div class="flex items-center mb-3">
                                <span class="inline-flex items-center justify-center w-6 h-6 bg-harrier-red text-white text-xs font-bold rounded-full mr-3">
                                    {{ step.step }}
                                </span>
                                <h3 class="text-base font-montserrat font-bold text-harrier-dark">{{ step.title }}</h3>
                            </div>
                            <p class="text-gray-600 font-raleway">{{ step.description }}</p>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>

        <!-- Enhanced CTA Button -->
        <div class="text-center mt-12">
            <a href="{% url 'core:import_request' %}"
               class="admin-request-add-btn inline-flex items-center px-10 py-5 text-lg font-montserrat font-semibold transition-all duration-500 hover:transform hover:scale-105 shadow-2xl group"
               aria-label="Start your import journey">
                <div class="flex items-center">
                    <i class="fas fa-rocket mr-3 text-xl group-hover:translate-x-1 transition-transform duration-300"></i>
                    START YOUR IMPORT JOURNEY
                    <i class="fas fa-arrow-right ml-3 group-hover:translate-x-1 transition-transform duration-300"></i>
                </div>
            </a>
        </div>
    </div>
</section>

<!-- Recent Imports -->
{% if recent_imports %}
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-heading font-bold text-harrier-dark mb-4">
                RECENT SUCCESSFUL IMPORTS
            </h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                See what our satisfied customers have successfully imported through our platform
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% for import in recent_imports %}
                <div class="bg-harrier-gray rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
                    <div class="h-48 bg-gradient-to-br from-green-500 to-green-600 relative">
                        <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-center text-white">
                                <i class="fas fa-check-circle text-4xl mb-2"></i>
                                <p class="text-sm font-semibold">COMPLETED</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-lg font-heading font-bold text-harrier-dark mb-2">
                            {{ import.brand }} {{ import.model }} ({{ import.year }})
                        </h3>
                        <div class="space-y-2 text-sm text-gray-600">
                            <div class="flex items-center justify-between">
                                <span>Origin:</span>
                                <span class="font-semibold">{{ import.origin_country }}</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span>Budget Range:</span>
                                <span class="font-semibold">KES {{ import.budget_min|floatformat:0 }} - {{ import.budget_max|floatformat:0 }}</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span>Completed:</span>
                                <span class="font-semibold">{{ import.updated_at|date:"M Y" }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Enhanced Why Choose Our Import Service with Glassmorphism Stats -->
<section class="py-16 relative overflow-hidden bg-gradient-to-br from-harrier-dark via-gray-900 to-black">
    <!-- Animated Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-20 left-10 w-64 h-64 bg-harrier-red bg-opacity-10 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-20 right-10 w-80 h-80 bg-red-600 bg-opacity-10 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-harrier-red bg-opacity-5 rounded-full blur-3xl animate-pulse" style="animation-delay: 1s;"></div>
    </div>

    <!-- Subtle Grid Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: linear-gradient(rgba(220,38,38,0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(220,38,38,0.1) 1px, transparent 1px); background-size: 50px 50px;"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <!-- Enhanced Section Header -->
        <div class="text-center mb-16 animate-entrance">
            <div class="inline-flex items-center justify-center w-14 h-14 bg-gradient-to-br from-harrier-red to-red-800 rounded-full mb-4 shadow-2xl">
                <i class="fas fa-award text-white text-xl"></i>
            </div>
            <h2 class="text-3xl md:text-4xl font-montserrat font-bold text-white mb-4 tracking-tight">
                WHY CHOOSE OUR IMPORT SERVICE?
            </h2>
            <p class="text-lg text-gray-300 max-w-4xl mx-auto font-raleway leading-relaxed">
                We've helped thousands of customers import their dream cars with complete peace of mind
            </p>
        </div>

        <!-- Enhanced Stats Grid with Glassmorphism -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            <!-- Trusted & Secure -->
            <div class="glassmorphism-stat-card group">
                <div class="relative">
                    <!-- Icon Container -->
                    <div class="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-harrier-red to-red-800 rounded-2xl mx-auto mb-4 shadow-2xl transition-all duration-500 group-hover:scale-110 group-hover:rotate-3">
                        <i class="fas fa-shield-alt text-white text-2xl"></i>
                    </div>

                    <!-- Content -->
                    <div class="text-center">
                        <h3 class="text-xl font-montserrat font-bold text-white mb-3 group-hover:text-harrier-red transition-colors duration-300">
                            Trusted & Secure
                        </h3>
                        <p class="text-gray-300 font-raleway leading-relaxed mb-6">
                            All transactions are secure and we work only with verified dealers and auction houses worldwide.
                        </p>

                        <!-- Stat Number -->
                        <div class="inline-flex items-center px-4 py-2 bg-white bg-opacity-10 rounded-full border border-white border-opacity-20">
                            <i class="fas fa-check-circle text-green-400 mr-2"></i>
                            <span class="text-white font-semibold">100% Verified</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transparent Pricing -->
            <div class="glassmorphism-stat-card group">
                <div class="relative">
                    <div class="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-harrier-red to-red-800 rounded-2xl mx-auto mb-4 shadow-2xl transition-all duration-500 group-hover:scale-110 group-hover:rotate-3">
                        <i class="fas fa-dollar-sign text-white text-2xl"></i>
                    </div>

                    <div class="text-center">
                        <h3 class="text-xl font-montserrat font-bold text-white mb-3 group-hover:text-harrier-red transition-colors duration-300">
                            Transparent Pricing
                        </h3>
                        <p class="text-gray-300 font-raleway leading-relaxed mb-4">
                            No hidden costs. We provide detailed breakdown of all charges including shipping and customs.
                        </p>

                        <div class="inline-flex items-center px-4 py-2 bg-white bg-opacity-10 rounded-full border border-white border-opacity-20">
                            <i class="fas fa-eye text-blue-400 mr-2"></i>
                            <span class="text-white font-semibold">No Hidden Fees</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Expert Support -->
            <div class="glassmorphism-stat-card group">
                <div class="relative">
                    <div class="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-harrier-red to-red-800 rounded-2xl mx-auto mb-4 shadow-2xl transition-all duration-500 group-hover:scale-110 group-hover:rotate-3">
                        <i class="fas fa-headset text-white text-2xl"></i>
                    </div>

                    <div class="text-center">
                        <h3 class="text-xl font-montserrat font-bold text-white mb-3 group-hover:text-harrier-red transition-colors duration-300">
                            Expert Support
                        </h3>
                        <p class="text-gray-300 font-raleway leading-relaxed mb-4">
                            Our experienced team guides you through every step of the import process.
                        </p>

                        <div class="inline-flex items-center px-4 py-2 bg-white bg-opacity-10 rounded-full border border-white border-opacity-20">
                            <i class="fas fa-clock text-yellow-400 mr-2"></i>
                            <span class="text-white font-semibold">24/7 Support</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Fast Processing -->
            <div class="glassmorphism-stat-card group">
                <div class="relative">
                    <div class="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-harrier-red to-red-800 rounded-2xl mx-auto mb-4 shadow-2xl transition-all duration-500 group-hover:scale-110 group-hover:rotate-3">
                        <i class="fas fa-clock text-white text-2xl"></i>
                    </div>

                    <div class="text-center">
                        <h3 class="text-xl font-montserrat font-bold text-white mb-3 group-hover:text-harrier-red transition-colors duration-300">
                            Fast Processing
                        </h3>
                        <p class="text-gray-300 font-raleway leading-relaxed mb-4">
                            Quick response times and efficient processing to get your car to you faster.
                        </p>

                        <div class="inline-flex items-center px-4 py-2 bg-white bg-opacity-10 rounded-full border border-white border-opacity-20">
                            <i class="fas fa-bolt text-yellow-400 mr-2"></i>
                            <span class="text-white font-semibold">48hr Response</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Complete Documentation -->
            <div class="glassmorphism-stat-card group">
                <div class="relative">
                    <div class="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-harrier-red to-red-800 rounded-2xl mx-auto mb-4 shadow-2xl transition-all duration-500 group-hover:scale-110 group-hover:rotate-3">
                        <i class="fas fa-file-alt text-white text-2xl"></i>
                    </div>

                    <div class="text-center">
                        <h3 class="text-xl font-montserrat font-bold text-white mb-3 group-hover:text-harrier-red transition-colors duration-300">
                            Complete Documentation
                        </h3>
                        <p class="text-gray-300 font-raleway leading-relaxed mb-4">
                            We handle all paperwork, customs clearance, and registration processes.
                        </p>

                        <div class="inline-flex items-center px-4 py-2 bg-white bg-opacity-10 rounded-full border border-white border-opacity-20">
                            <i class="fas fa-clipboard-check text-green-400 mr-2"></i>
                            <span class="text-white font-semibold">Full Service</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Door-to-Door Delivery -->
            <div class="glassmorphism-stat-card group">
                <div class="relative">
                    <div class="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-harrier-red to-red-800 rounded-2xl mx-auto mb-4 shadow-2xl transition-all duration-500 group-hover:scale-110 group-hover:rotate-3">
                        <i class="fas fa-truck text-white text-2xl"></i>
                    </div>

                    <div class="text-center">
                        <h3 class="text-xl font-montserrat font-bold text-white mb-3 group-hover:text-harrier-red transition-colors duration-300">
                            Door-to-Door Delivery
                        </h3>
                        <p class="text-gray-300 font-raleway leading-relaxed mb-4">
                            We deliver your imported car directly to your preferred location in Kenya.
                        </p>

                        <div class="inline-flex items-center px-4 py-2 bg-white bg-opacity-10 rounded-full border border-white border-opacity-20">
                            <i class="fas fa-map-marker-alt text-red-400 mr-2"></i>
                            <span class="text-white font-semibold">Nationwide</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success Stats Row -->
        <div class="mt-16 grid grid-cols-1 md:grid-cols-4 gap-6 max-w-5xl mx-auto">
            <div class="text-center">
                <div class="text-4xl font-montserrat font-bold text-harrier-red mb-2">5000+</div>
                <div class="text-gray-300 font-raleway">Cars Imported</div>
            </div>
            <div class="text-center">
                <div class="text-4xl font-montserrat font-bold text-harrier-red mb-2">98%</div>
                <div class="text-gray-300 font-raleway">Customer Satisfaction</div>
            </div>
            <div class="text-center">
                <div class="text-4xl font-montserrat font-bold text-harrier-red mb-2">15+</div>
                <div class="text-gray-300 font-raleway">Countries</div>
            </div>
            <div class="text-center">
                <div class="text-4xl font-montserrat font-bold text-harrier-red mb-2">24/7</div>
                <div class="text-gray-300 font-raleway">Support</div>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced CTA Section with Testimonials Carousel -->
<section class="py-16 relative overflow-hidden bg-gradient-to-br from-harrier-red via-red-700 to-harrier-dark">
    <!-- Glassmorphism Background Effects -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-10 left-10 w-72 h-72 bg-white bg-opacity-5 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-10 right-10 w-80 h-80 bg-white bg-opacity-5 rounded-full blur-3xl animate-pulse" style="animation-delay: 1.5s;"></div>
        <div class="absolute top-1/2 left-1/3 w-64 h-64 bg-white bg-opacity-3 rounded-full blur-3xl animate-pulse" style="animation-delay: 3s;"></div>
    </div>

    <!-- Subtle Pattern Overlay -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 2px 2px, rgba(255,255,255,0.3) 1px, transparent 0); background-size: 30px 30px;"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <!-- Testimonials Carousel -->
        <div class="mb-12">
            <div class="text-center mb-12">
                <h3 class="text-2xl font-montserrat font-bold text-white mb-4">What Our Customers Say</h3>
                <div class="w-24 h-1 bg-white bg-opacity-50 mx-auto rounded-full"></div>
            </div>

            <!-- Testimonials Slider -->
            <div class="max-w-4xl mx-auto">
                <div class="testimonials-carousel relative">
                    <!-- Testimonial 1 -->
                    <div class="testimonial-slide active bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-2xl p-6 border border-white border-opacity-20 shadow-2xl">
                        <div class="flex items-center mb-6">
                            <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-user text-white text-xl"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-montserrat font-bold text-white">James Mwangi</h4>
                                <div class="flex items-center">
                                    <div class="flex text-yellow-400 mr-2">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <span class="text-white text-opacity-80 text-sm">Toyota Prado from Japan</span>
                                </div>
                            </div>
                        </div>
                        <p class="text-white text-opacity-90 font-raleway text-lg leading-relaxed italic">
                            "Exceptional service from start to finish. The team handled everything professionally and my Toyota Prado arrived exactly as described. Highly recommend Gurumisha for anyone looking to import a quality vehicle."
                        </p>
                    </div>

                    <!-- Testimonial 2 -->
                    <div class="testimonial-slide bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-2xl p-6 border border-white border-opacity-20 shadow-2xl hidden">
                        <div class="flex items-center mb-6">
                            <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-user text-white text-xl"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-montserrat font-bold text-white">Sarah Wanjiku</h4>
                                <div class="flex items-center">
                                    <div class="flex text-yellow-400 mr-2">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <span class="text-white text-opacity-80 text-sm">BMW X5 from Germany</span>
                                </div>
                            </div>
                        </div>
                        <p class="text-white text-opacity-90 font-raleway text-lg leading-relaxed italic">
                            "Amazing experience! The transparency in pricing and constant updates throughout the shipping process gave me complete peace of mind. My BMW X5 is everything I hoped for and more."
                        </p>
                    </div>

                    <!-- Testimonial 3 -->
                    <div class="testimonial-slide bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-2xl p-6 border border-white border-opacity-20 shadow-2xl hidden">
                        <div class="flex items-center mb-6">
                            <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-user text-white text-xl"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-montserrat font-bold text-white">David Kiprotich</h4>
                                <div class="flex items-center">
                                    <div class="flex text-yellow-400 mr-2">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <span class="text-white text-opacity-80 text-sm">Range Rover from UK</span>
                                </div>
                            </div>
                        </div>
                        <p class="text-white text-opacity-90 font-raleway text-lg leading-relaxed italic">
                            "Outstanding service and support team. They found exactly what I was looking for and handled all the complex paperwork. The door-to-door delivery was seamless. Five stars!"
                        </p>
                    </div>

                    <!-- Carousel Navigation -->
                    <div class="flex justify-center mt-8 space-x-3">
                        <button class="carousel-dot active w-3 h-3 bg-white rounded-full transition-all duration-300" data-slide="0"></button>
                        <button class="carousel-dot w-3 h-3 bg-white bg-opacity-50 rounded-full transition-all duration-300" data-slide="1"></button>
                        <button class="carousel-dot w-3 h-3 bg-white bg-opacity-50 rounded-full transition-all duration-300" data-slide="2"></button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced CTA Content -->
        <div class="max-w-5xl mx-auto text-center">
            <div class="mb-8">
                <h2 class="text-3xl md:text-4xl font-montserrat font-bold text-white mb-4 tracking-tight">
                    Ready to Import Your <span class="text-yellow-400">Dream Car</span>?
                </h2>
                <p class="text-lg md:text-xl text-white text-opacity-90 max-w-3xl mx-auto font-raleway leading-relaxed">
                    Join thousands of satisfied customers who have successfully imported their cars through our platform
                </p>
            </div>

            <!-- Enhanced CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <!-- Primary CTA Button (Admin Style) -->
                <a href="{% url 'core:import_request' %}"
                   class="admin-request-add-btn group inline-flex items-center px-8 py-4 text-lg font-montserrat font-bold transition-all duration-500 hover:transform hover:scale-105 shadow-2xl relative overflow-hidden"
                   aria-label="Start your import request">
                    <div class="flex items-center relative z-10">
                        <i class="fas fa-paper-plane mr-4 text-2xl group-hover:translate-x-1 transition-transform duration-300"></i>
                        START IMPORT REQUEST
                        <i class="fas fa-arrow-right ml-4 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                    <!-- Button Glow Effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-white via-transparent to-white opacity-0 group-hover:opacity-20 transition-opacity duration-500"></div>
                </a>

                <!-- Secondary CTA Button -->
                <a href="{% url 'core:contact_us' %}"
                   class="group inline-flex items-center px-6 py-3 bg-white bg-opacity-10 hover:bg-opacity-20 backdrop-filter backdrop-blur-lg border-2 border-white border-opacity-50 hover:border-opacity-80 rounded-xl text-white font-montserrat font-semibold text-base transition-all duration-500 hover:transform hover:scale-105 shadow-xl"
                   aria-label="Speak to our import expert">
                    <i class="fas fa-phone mr-3 text-xl group-hover:rotate-12 transition-transform duration-300"></i>
                    SPEAK TO EXPERT
                    <i class="fas fa-arrow-right ml-3 group-hover:translate-x-1 transition-transform duration-300"></i>
                </a>
            </div>

            <!-- Trust Indicators -->
            <div class="mt-12 flex flex-wrap justify-center items-center gap-8 text-white text-opacity-80">
                <div class="flex items-center">
                    <i class="fas fa-shield-alt text-green-400 mr-2"></i>
                    <span class="font-raleway">Secure & Trusted</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-clock text-yellow-400 mr-2"></i>
                    <span class="font-raleway">24/7 Support</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-award text-blue-400 mr-2"></i>
                    <span class="font-raleway">5000+ Happy Customers</span>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
    /* Enhanced Import Page Styles with Glassmorphism */

    /* Glassmorphism Stat Cards */
    .glassmorphism-stat-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 1.5rem;
        box-shadow:
            0 6px 24px rgba(0, 0, 0, 0.3),
            0 3px 12px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .glassmorphism-stat-card:hover {
        transform: translateY(-6px) scale(1.02);
        box-shadow:
            0 16px 32px rgba(0, 0, 0, 0.4),
            0 6px 20px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        border-color: rgba(220, 38, 38, 0.3);
    }

    .glassmorphism-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.8s ease;
    }

    .glassmorphism-stat-card:hover::before {
        left: 100%;
    }

    /* Destination Cards */
    .destination-card {
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .destination-card:hover {
        transform: translateY(-6px);
    }

    /* Process Steps */
    .process-step {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .process-step:hover {
        transform: translateY(-3px);
    }

    /* Admin Request Add Button Styles */
    .admin-request-add-btn {
        background: linear-gradient(135deg, var(--harrier-red) 0%, var(--harrier-dark) 100%);
        color: white;
        border: none;
        border-radius: 14px;
        padding: 0.875rem 1.75rem;
        font-weight: 700;
        font-size: 0.95rem;
        cursor: pointer;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        display: inline-flex;
        align-items: center;
        text-decoration: none;
        font-family: 'Montserrat', sans-serif;
        box-shadow:
            0 6px 20px rgba(220, 38, 38, 0.4),
            0 3px 10px rgba(31, 41, 55, 0.3);
        position: relative;
        overflow: hidden;
    }

    .admin-request-add-btn:hover {
        transform: translateY(-3px) scale(1.03);
        box-shadow:
            0 12px 28px rgba(220, 38, 38, 0.5),
            0 6px 16px rgba(31, 41, 55, 0.4);
        color: white;
        text-decoration: none;
    }

    .admin-request-add-btn:active {
        transform: translateY(-2px) scale(1.02);
    }

    /* Testimonials Carousel */
    .testimonials-carousel {
        position: relative;
    }

    .testimonial-slide {
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        opacity: 0;
        transform: translateX(50px);
    }

    .testimonial-slide.active {
        opacity: 1;
        transform: translateX(0);
    }

    .carousel-dot {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .carousel-dot.active {
        background-color: white !important;
        transform: scale(1.2);
    }

    .carousel-dot:hover {
        background-color: rgba(255, 255, 255, 0.8) !important;
        transform: scale(1.1);
    }

    /* Entrance Animations */
    .animate-entrance {
        opacity: 0;
        transform: translateY(30px);
        animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }

    @keyframes fadeInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .glassmorphism-stat-card {
            padding: 1.25rem;
        }

        .destination-card {
            margin-bottom: 0.75rem;
        }

        .admin-request-add-btn {
            padding: 0.75rem 1.25rem;
            font-size: 0.875rem;
        }

        /* Compact mobile spacing */
        .py-16 {
            padding-top: 3rem !important;
            padding-bottom: 3rem !important;
        }

        .mb-12 {
            margin-bottom: 2rem !important;
        }

        .mb-16 {
            margin-bottom: 2.5rem !important;
        }
    }

    /* CSS Variables for Consistency */
    :root {
        --harrier-red: #DC2626;
        --harrier-red-dark: #B91C1C;
        --harrier-dark: #1F2937;
        --harrier-blue: #1E3A8A;
        --harrier-blue-light: #3B82F6;
        --harrier-white: #FFFFFF;
        --harrier-gray: #F9FAFB;
        --harrier-gray-dark: #6B7280;
    }

    /* High Performance Mode */
    @media (prefers-reduced-motion: reduce) {
        .glassmorphism-stat-card,
        .destination-card,
        .process-step,
        .admin-request-add-btn,
        .testimonial-slide {
            transition: none;
            animation: none;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
    // Enhanced Import Page JavaScript with HTMX Integration

    document.addEventListener('DOMContentLoaded', function() {
        // Testimonials Carousel Functionality
        initTestimonialsCarousel();

        // Intersection Observer for Entrance Animations
        initEntranceAnimations();

        // Enhanced Hover Effects
        initHoverEffects();
    });

    function initTestimonialsCarousel() {
        const slides = document.querySelectorAll('.testimonial-slide');
        const dots = document.querySelectorAll('.carousel-dot');
        let currentSlide = 0;

        // Auto-rotate testimonials
        setInterval(() => {
            showSlide((currentSlide + 1) % slides.length);
        }, 5000);

        // Dot navigation
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                showSlide(index);
            });
        });

        function showSlide(index) {
            // Hide all slides
            slides.forEach(slide => {
                slide.classList.remove('active');
                slide.classList.add('hidden');
            });

            // Remove active state from all dots
            dots.forEach(dot => {
                dot.classList.remove('active');
                dot.classList.add('bg-white', 'bg-opacity-50');
                dot.classList.remove('bg-white');
            });

            // Show current slide
            if (slides[index]) {
                slides[index].classList.add('active');
                slides[index].classList.remove('hidden');
            }

            // Activate current dot
            if (dots[index]) {
                dots[index].classList.add('active');
                dots[index].classList.remove('bg-white', 'bg-opacity-50');
                dots[index].classList.add('bg-white');
            }

            currentSlide = index;
        }
    }

    function initEntranceAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationDelay = '0.2s';
                    entry.target.classList.add('animate-entrance');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe elements for entrance animations
        document.querySelectorAll('.glassmorphism-stat-card, .destination-card, .process-step').forEach(el => {
            observer.observe(el);
        });
    }

    function initHoverEffects() {
        // Enhanced destination card hover effects
        document.querySelectorAll('.destination-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-6px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Process step hover effects
        document.querySelectorAll('.process-step').forEach(step => {
            step.addEventListener('mouseenter', function() {
                const circle = this.querySelector('.w-20.h-20');
                if (circle) {
                    circle.style.transform = 'scale(1.08) rotate(3deg)';
                }
            });

            step.addEventListener('mouseleave', function() {
                const circle = this.querySelector('.w-20.h-20');
                if (circle) {
                    circle.style.transform = 'scale(1) rotate(0deg)';
                }
            });
        });
    }

    // HTMX Integration for Dynamic Content
    document.addEventListener('htmx:afterRequest', function(event) {
        // Reinitialize animations after HTMX requests
        if (event.detail.successful) {
            initEntranceAnimations();
            initHoverEffects();
        }
    });

    // Performance optimization
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
</script>
{% endblock %}
