{% extends 'base.html' %}
{% load static %}

{% block title %}Car Finance Calculator - Gurumisha{% endblock %}
{% block meta_description %}Calculate your car loan payments with our comprehensive financing calculator. Get instant estimates for monthly payments, total cost, and interest.{% endblock %}

{% block extra_css %}
<style>
/* Enhanced Car Calculator Styles with Harrier Design */
.calculator-hero {
    background: linear-gradient(135deg, #DC2626 0%, #1F2937 50%, #1E3A8A 100%);
    position: relative;
    overflow: hidden;
}

.calculator-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.calculator-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1.5rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    transition: all 0.3s ease;
}

.calculator-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-family: 'Montserrat', sans-serif;
}

.form-input {
    width: 100%;
    padding: 1rem 1.5rem;
    border: 2px solid #E5E7EB;
    border-radius: 0.75rem;
    font-size: 1rem;
    font-family: 'Raleway', sans-serif;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
}

.form-input:focus {
    outline: none;
    border-color: #DC2626;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    background: rgba(255, 255, 255, 1);
}

.range-input {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: #E5E7EB;
    outline: none;
    -webkit-appearance: none;
}

.range-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #DC2626, #EF4444);
    cursor: pointer;
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
}

.range-input::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #DC2626, #EF4444);
    cursor: pointer;
    border: none;
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
}

.calculate-button {
    background: linear-gradient(135deg, #DC2626, #EF4444);
    color: white;
    padding: 1rem 2rem;
    border-radius: 1rem;
    font-weight: 700;
    font-family: 'Montserrat', sans-serif;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    width: 100%;
    font-size: 1.1rem;
}

.calculate-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 30px -5px rgba(220, 38, 38, 0.4);
    background: linear-gradient(135deg, #B91C1C, #DC2626);
}

.calculate-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.results-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 1.5rem;
    padding: 2rem;
    margin-top: 2rem;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease;
}

.results-card.show {
    opacity: 1;
    transform: translateY(0);
}

.result-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(229, 231, 235, 0.3);
}

.result-item:last-child {
    border-bottom: none;
}

.result-label {
    font-weight: 600;
    color: #374151;
    font-family: 'Montserrat', sans-serif;
}

.result-value {
    font-weight: 700;
    color: #1F2937;
    font-family: 'Montserrat', sans-serif;
    font-size: 1.1rem;
}

.result-value.highlight {
    color: #DC2626;
    font-size: 1.5rem;
}

.breakdown-chart {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.chart-bar {
    height: 20px;
    border-radius: 10px;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
    background: #F3F4F6;
}

.chart-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.8s ease;
}

.chart-fill.principal {
    background: linear-gradient(135deg, #DC2626, #EF4444);
}

.chart-fill.interest {
    background: linear-gradient(135deg, #F59E0B, #FBBF24);
}

.chart-fill.insurance {
    background: linear-gradient(135deg, #10B981, #34D399);
}

.chart-fill.fees {
    background: linear-gradient(135deg, #8B5CF6, #A78BFA);
}

.chart-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-family: 'Raleway', sans-serif;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 4px;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }

@media (max-width: 768px) {
    .calculator-card {
        padding: 1.5rem;
    }
    
    .results-card {
        padding: 1.5rem;
    }
    
    .result-value.highlight {
        font-size: 1.25rem;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<div class="bg-harrier-gray py-4">
    <div class="container mx-auto px-4">
        <nav class="text-sm">
            <ol class="flex items-center space-x-2">
                <li><a href="{% url 'core:homepage' %}" class="text-harrier-dark hover:text-harrier-red">Home</a></li>
                <li class="text-gray-500">&rsaquo;</li>
                {% if car %}
                <li><a href="{% url 'core:car_list' %}" class="text-harrier-dark hover:text-harrier-red">Cars</a></li>
                <li class="text-gray-500">&rsaquo;</li>
                <li><a href="{% url 'core:car_detail' car.pk %}" class="text-harrier-dark hover:text-harrier-red">{{ car.title }}</a></li>
                <li class="text-gray-500">&rsaquo;</li>
                {% endif %}
                <li class="text-harrier-red font-semibold">Finance Calculator</li>
            </ol>
        </nav>
    </div>
</div>

<!-- Hero Section -->
<section class="calculator-hero py-16 relative">
    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="animate-fade-in-up">
            <h1 class="text-5xl lg:text-6xl font-bold text-white mb-6 font-montserrat">
                Car Finance <span class="text-transparent bg-clip-text bg-gradient-to-r from-red-300 to-yellow-300">Calculator</span>
            </h1>
            <p class="text-xl text-blue-100 mb-8 max-w-3xl mx-auto font-raleway">
                Calculate your monthly payments and total cost with our comprehensive financing calculator
            </p>
            {% if car %}
            <div class="inline-flex items-center px-6 py-3 bg-white/20 rounded-full text-white font-semibold backdrop-blur-sm">
                <i class="fas fa-car mr-2"></i>
                <span>Calculating for: {{ car.title }}</span>
            </div>
            {% endif %}
        </div>
    </div>
</section>

<!-- Calculator Section -->
<section class="py-16 bg-gradient-to-br from-gray-50 to-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Calculator Form -->
            <div class="animate-fade-in-up">
                <div class="calculator-card p-8">
                    <h2 class="text-3xl font-bold text-harrier-dark mb-8 font-montserrat">Loan Calculator</h2>
                    
                    <form id="calculatorForm">
                        {% csrf_token %}
                        
                        <!-- Car Price -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-tag text-harrier-red mr-2"></i>Car Price (KES)
                            </label>
                            <input type="number" id="carPrice" name="car_price" class="form-input" 
                                   value="{{ default_price|floatformat:0 }}" min="0" step="1000" required>
                        </div>
                        
                        <!-- Down Payment -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-hand-holding-usd text-harrier-red mr-2"></i>Down Payment
                            </label>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <input type="range" id="downPaymentRange" class="range-input" 
                                           min="0" max="50" value="{{ default_down_payment|floatformat:0 }}" step="1">
                                    <div class="text-center mt-2">
                                        <span id="downPaymentPercent" class="font-semibold text-harrier-red">{{ default_down_payment|floatformat:0 }}%</span>
                                    </div>
                                </div>
                                <div>
                                    <input type="number" id="downPaymentAmount" class="form-input" readonly>
                                    <div class="text-center mt-2 text-sm text-gray-600">Amount (KES)</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Loan Term -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-calendar text-harrier-red mr-2"></i>Loan Term
                            </label>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <input type="range" id="loanTermRange" class="range-input" 
                                           min="12" max="84" value="{{ default_loan_term }}" step="12">
                                    <div class="text-center mt-2">
                                        <span id="loanTermMonths" class="font-semibold text-harrier-red">{{ default_loan_term }} months</span>
                                    </div>
                                </div>
                                <div>
                                    <span id="loanTermYears" class="form-input bg-gray-100 flex items-center justify-center">5 years</span>
                                    <div class="text-center mt-2 text-sm text-gray-600">Duration</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Interest Rate -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-percentage text-harrier-red mr-2"></i>Interest Rate (% per year)
                            </label>
                            <input type="number" id="interestRate" name="interest_rate" class="form-input" 
                                   value="{{ default_interest_rate }}" min="0" max="30" step="0.1" required>
                        </div>
                        
                        <!-- Insurance -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-shield-alt text-harrier-red mr-2"></i>Monthly Insurance (KES)
                            </label>
                            <input type="number" id="insurance" name="insurance_monthly" class="form-input" 
                                   value="0" min="0" step="100">
                        </div>
                        
                        <!-- Other Fees -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-receipt text-harrier-red mr-2"></i>Other Monthly Fees (KES)
                            </label>
                            <input type="number" id="otherFees" name="other_fees_monthly" class="form-input" 
                                   value="0" min="0" step="100">
                        </div>
                        
                        <!-- Calculate Button -->
                        <button type="submit" class="calculate-button" id="calculateBtn">
                            <i class="fas fa-calculator mr-2"></i>Calculate Payment
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Results -->
            <div class="animate-fade-in-up animate-delay-200">
                <div id="resultsCard" class="results-card">
                    <h2 class="text-3xl font-bold text-harrier-dark mb-8 font-montserrat">Payment Breakdown</h2>
                    
                    <div id="resultsContent">
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-calculator text-6xl mb-4"></i>
                            <p class="text-lg font-raleway">Enter your details and click calculate to see results</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
// Calculator functionality
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('calculatorForm');
    const carPriceInput = document.getElementById('carPrice');
    const downPaymentRange = document.getElementById('downPaymentRange');
    const downPaymentPercent = document.getElementById('downPaymentPercent');
    const downPaymentAmount = document.getElementById('downPaymentAmount');
    const loanTermRange = document.getElementById('loanTermRange');
    const loanTermMonths = document.getElementById('loanTermMonths');
    const loanTermYears = document.getElementById('loanTermYears');
    const resultsCard = document.getElementById('resultsCard');
    const resultsContent = document.getElementById('resultsContent');
    const calculateBtn = document.getElementById('calculateBtn');
    
    // Update down payment amount when price or percentage changes
    function updateDownPayment() {
        const price = parseFloat(carPriceInput.value) || 0;
        const percent = parseFloat(downPaymentRange.value);
        const amount = price * (percent / 100);
        
        downPaymentPercent.textContent = percent + '%';
        downPaymentAmount.value = Math.round(amount).toLocaleString();
    }
    
    // Update loan term display
    function updateLoanTerm() {
        const months = parseInt(loanTermRange.value);
        const years = (months / 12).toFixed(1);
        
        loanTermMonths.textContent = months + ' months';
        loanTermYears.textContent = years + ' years';
    }
    
    // Event listeners
    carPriceInput.addEventListener('input', updateDownPayment);
    downPaymentRange.addEventListener('input', updateDownPayment);
    loanTermRange.addEventListener('input', updateLoanTerm);
    
    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        calculateBtn.disabled = true;
        calculateBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Calculating...';
        
        const formData = new FormData();
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
        formData.append('car_price', carPriceInput.value);
        formData.append('down_payment_percent', downPaymentRange.value);
        formData.append('loan_term_months', loanTermRange.value);
        formData.append('interest_rate', document.getElementById('interestRate').value);
        formData.append('insurance_monthly', document.getElementById('insurance').value);
        formData.append('other_fees_monthly', document.getElementById('otherFees').value);
        
        fetch('/calculator/calculate/', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayResults(data.calculations);
                resultsCard.classList.add('show');
            } else {
                showNotification(data.error || 'Calculation failed', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred during calculation', 'error');
        })
        .finally(() => {
            calculateBtn.disabled = false;
            calculateBtn.innerHTML = '<i class="fas fa-calculator mr-2"></i>Calculate Payment';
        });
    });
    
    // Display calculation results
    function displayResults(calc) {
        const formatCurrency = (amount) => {
            return 'KSH ' + Math.round(amount).toLocaleString();
        };
        
        resultsContent.innerHTML = `
            <div class="space-y-4">
                <!-- Monthly Payment Highlight -->
                <div class="result-item">
                    <span class="result-label">Monthly Payment</span>
                    <span class="result-value highlight">${formatCurrency(calc.total_monthly_payment)}</span>
                </div>
                
                <!-- Breakdown -->
                <div class="result-item">
                    <span class="result-label">Loan Payment</span>
                    <span class="result-value">${formatCurrency(calc.monthly_payment)}</span>
                </div>
                
                <div class="result-item">
                    <span class="result-label">Insurance</span>
                    <span class="result-value">${formatCurrency(calc.insurance_monthly)}</span>
                </div>
                
                <div class="result-item">
                    <span class="result-label">Other Fees</span>
                    <span class="result-value">${formatCurrency(calc.other_fees_monthly)}</span>
                </div>
                
                <!-- Loan Details -->
                <div class="result-item">
                    <span class="result-label">Down Payment</span>
                    <span class="result-value">${formatCurrency(calc.down_payment_amount)}</span>
                </div>
                
                <div class="result-item">
                    <span class="result-label">Loan Amount</span>
                    <span class="result-value">${formatCurrency(calc.loan_amount)}</span>
                </div>
                
                <div class="result-item">
                    <span class="result-label">Total Interest</span>
                    <span class="result-value">${formatCurrency(calc.total_interest)}</span>
                </div>
                
                <div class="result-item">
                    <span class="result-label">Total Cost</span>
                    <span class="result-value highlight">${formatCurrency(calc.total_cost)}</span>
                </div>
            </div>
            
            <!-- Visual Breakdown -->
            <div class="breakdown-chart">
                <h3 class="text-lg font-bold text-harrier-dark mb-4 font-montserrat">Cost Breakdown</h3>
                
                <div class="space-y-3">
                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span>Principal</span>
                            <span>${formatCurrency(calc.loan_amount)}</span>
                        </div>
                        <div class="chart-bar">
                            <div class="chart-fill principal" style="width: ${(calc.loan_amount / calc.total_cost * 100)}%"></div>
                        </div>
                    </div>
                    
                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span>Interest</span>
                            <span>${formatCurrency(calc.total_interest)}</span>
                        </div>
                        <div class="chart-bar">
                            <div class="chart-fill interest" style="width: ${(calc.total_interest / calc.total_cost * 100)}%"></div>
                        </div>
                    </div>
                    
                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span>Insurance</span>
                            <span>${formatCurrency(calc.total_insurance)}</span>
                        </div>
                        <div class="chart-bar">
                            <div class="chart-fill insurance" style="width: ${(calc.total_insurance / calc.total_cost * 100)}%"></div>
                        </div>
                    </div>
                    
                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span>Other Fees</span>
                            <span>${formatCurrency(calc.total_other_fees)}</span>
                        </div>
                        <div class="chart-bar">
                            <div class="chart-fill fees" style="width: ${(calc.total_other_fees / calc.total_cost * 100)}%"></div>
                        </div>
                    </div>
                </div>
                
                <div class="chart-legend">
                    <div class="legend-item">
                        <div class="legend-color principal"></div>
                        <span>Principal</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color interest"></div>
                        <span>Interest</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color insurance"></div>
                        <span>Insurance</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color fees"></div>
                        <span>Other Fees</span>
                    </div>
                </div>
            </div>
        `;
    }
    
    // Notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-6 py-4 rounded-lg text-white font-semibold transform translate-x-full transition-transform duration-300`;
        
        switch(type) {
            case 'success':
                notification.classList.add('bg-green-500');
                break;
            case 'error':
                notification.classList.add('bg-red-500');
                break;
            default:
                notification.classList.add('bg-blue-500');
        }
        
        notification.innerHTML = `
            <div class="flex items-center">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => notification.remove(), 300);
        }, 5000);
    }
    
    // Initialize
    updateDownPayment();
    updateLoanTerm();
    resultsCard.classList.add('show');
});
</script>
{% endblock %}
