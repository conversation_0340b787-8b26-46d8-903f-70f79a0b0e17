{% extends 'base.html' %}
{% load static %}

{% block title %}Our Dealers - Gurumisha{% endblock %}
{% block meta_description %}Browse our network of verified car dealers and find the perfect vehicle from trusted automotive professionals.{% endblock %}

{% block extra_css %}
<style>
/* Enhanced Dealer List Styles with Harrier Design */
.dealer-hero {
    background: linear-gradient(135deg, #DC2626 0%, #1F2937 50%, #1E3A8A 100%);
    position: relative;
    overflow: hidden;
}

.dealer-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.dealer-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.dealer-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    background: rgba(255, 255, 255, 1);
}

.dealer-avatar {
    width: 80px;
    height: 80px;
    border: 4px solid rgba(220, 38, 38, 0.2);
    transition: all 0.3s ease;
}

.dealer-card:hover .dealer-avatar {
    border-color: #DC2626;
    transform: scale(1.05);
}

.stat-badge {
    background: linear-gradient(135deg, #DC2626, #EF4444);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.dealer-status {
    background: linear-gradient(135deg, #10B981, #34D399);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="dealer-hero py-20 relative">
    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="animate-fade-in-up">
            <h1 class="text-5xl lg:text-6xl font-bold text-white mb-6 font-montserrat">
                Our Trusted <span class="text-transparent bg-clip-text bg-gradient-to-r from-red-300 to-yellow-300">Dealers</span>
            </h1>
            <p class="text-xl text-blue-100 mb-8 max-w-3xl mx-auto font-raleway">
                Connect with verified automotive professionals who offer quality vehicles and exceptional service
            </p>
            <div class="flex flex-col sm:flex-row items-center justify-center gap-6">
                <div class="stat-badge animate-delay-100">
                    <i class="fas fa-users"></i>
                    <span>{{ total_dealers }} Verified Dealers</span>
                </div>
                <div class="stat-badge animate-delay-200">
                    <i class="fas fa-shield-check"></i>
                    <span>100% Verified</span>
                </div>
                <div class="stat-badge animate-delay-300">
                    <i class="fas fa-star"></i>
                    <span>Top Rated Service</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Dealers Grid -->
<section class="py-16 bg-gradient-to-br from-gray-50 to-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="text-center mb-12 animate-fade-in-up">
            <h2 class="text-3xl font-bold text-harrier-dark mb-4 font-montserrat">Browse Our Dealer Network</h2>
            <p class="text-lg text-gray-600 font-raleway">Find the perfect dealer for your automotive needs</p>
        </div>

        <!-- Dealers Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% for vendor in vendors %}
            <div class="dealer-card rounded-2xl p-6 animate-fade-in-up" style="animation-delay: {{ forloop.counter0|add:1|floatformat:1 }}00ms;">
                <!-- Dealer Header -->
                <div class="flex items-start justify-between mb-6">
                    <div class="flex items-center space-x-4">
                        <!-- Dealer Avatar -->
                        <div class="dealer-avatar rounded-2xl overflow-hidden flex-shrink-0">
                            {% if vendor.company_logo %}
                                <img src="{{ vendor.company_logo.url }}" alt="{{ vendor.company_name }}" class="w-full h-full object-cover">
                            {% else %}
                                <div class="w-full h-full bg-gradient-to-br from-harrier-red to-harrier-blue flex items-center justify-center text-white font-bold text-2xl">
                                    {{ vendor.company_name|first|upper }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Dealer Info -->
                        <div class="flex-1 min-w-0">
                            <h3 class="text-xl font-bold text-harrier-dark mb-1 font-montserrat truncate">{{ vendor.company_name }}</h3>
                            <p class="text-sm text-gray-600 font-raleway">{{ vendor.get_business_type_display }}</p>
                            {% if vendor.is_approved %}
                                <span class="dealer-status mt-2 inline-block">
                                    <i class="fas fa-shield-check mr-1"></i>Verified
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Dealer Description -->
                {% if vendor.description %}
                <p class="text-gray-700 text-sm mb-6 line-clamp-3 font-raleway">{{ vendor.description|truncatewords:20 }}</p>
                {% endif %}

                <!-- Dealer Stats -->
                <div class="grid grid-cols-3 gap-4 mb-6">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-harrier-red font-montserrat">{{ vendor.total_listings }}</div>
                        <div class="text-xs text-gray-600 font-raleway">Listings</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-harrier-blue font-montserrat">{{ vendor.total_views|default:0 }}</div>
                        <div class="text-xs text-gray-600 font-raleway">Views</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-harrier-dark font-montserrat">{{ vendor.average_rating|floatformat:1|default:"5.0" }}</div>
                        <div class="text-xs text-gray-600 font-raleway">Rating</div>
                    </div>
                </div>

                <!-- Contact Info -->
                <div class="space-y-2 mb-6">
                    {% if vendor.business_phone %}
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-phone text-harrier-red mr-2 w-4"></i>
                        <span class="font-raleway">{{ vendor.business_phone }}</span>
                    </div>
                    {% endif %}
                    {% if vendor.physical_address %}
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-map-marker-alt text-harrier-red mr-2 w-4"></i>
                        <span class="font-raleway truncate">{{ vendor.physical_address|truncatewords:5 }}</span>
                    </div>
                    {% endif %}
                </div>

                <!-- Action Buttons -->
                <div class="flex space-x-3">
                    <a href="{% url 'core:dealer_profile' vendor.id %}" 
                       class="flex-1 bg-gradient-to-r from-harrier-red to-red-600 text-white text-center py-3 px-4 rounded-xl font-semibold text-sm hover:from-red-600 hover:to-red-700 transition-all duration-300 font-montserrat">
                        <i class="fas fa-user mr-2"></i>View Profile
                    </a>
                    <a href="{% url 'core:car_list' %}?vendor={{ vendor.id }}" 
                       class="flex-1 bg-gradient-to-r from-harrier-blue to-blue-700 text-white text-center py-3 px-4 rounded-xl font-semibold text-sm hover:from-blue-700 hover:to-blue-800 transition-all duration-300 font-montserrat">
                        <i class="fas fa-car mr-2"></i>View Cars
                    </a>
                </div>
            </div>
            {% empty %}
            <div class="col-span-full text-center py-16">
                <div class="text-gray-400 mb-4">
                    <i class="fas fa-users text-6xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-600 mb-2 font-montserrat">No Dealers Found</h3>
                <p class="text-gray-500 font-raleway">We're working on adding more verified dealers to our network.</p>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="py-16 bg-gradient-to-r from-harrier-red via-harrier-dark to-harrier-blue">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="animate-fade-in-up">
            <h2 class="text-3xl font-bold text-white mb-4 font-montserrat">Want to Join Our Dealer Network?</h2>
            <p class="text-xl text-blue-100 mb-8 font-raleway">
                Become a verified dealer and reach thousands of potential customers
            </p>
            <a href="{% url 'core:user_register' %}" 
               class="inline-flex items-center bg-white text-harrier-red px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-all duration-300 font-montserrat">
                <i class="fas fa-handshake mr-3"></i>
                Become a Dealer
            </a>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
// Enhanced animations and interactions
document.addEventListener('DOMContentLoaded', function() {
    // Animate cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe all dealer cards
    document.querySelectorAll('.dealer-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });

    // Add hover effects for better interactivity
    document.querySelectorAll('.dealer-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});
</script>
{% endblock %}
