{% load static %}

<div class="glassmorphism-card p-6">
    <!-- Enhanced Search and Filter Section -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
        <div class="flex-1 max-w-md">
            <div class="relative">
                <input type="text" 
                       id="infographics-search" 
                       placeholder="Search infographics..." 
                       value="{{ search_query }}"
                       class="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-300 bg-white/80 backdrop-blur-sm"
                       hx-get="{% url 'core:admin_resource_search' %}"
                       hx-target="#resource-tab-content"
                       hx-trigger="keyup changed delay:300ms"
                       hx-include="[name='status'], [name='category']"
                       hx-vals='{"tab": "infographics", "content_type": "infographic"}'>
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
            </div>
        </div>

        <div class="flex flex-wrap gap-3">
            <!-- Status Filter -->
            <select name="status" 
                    class="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent bg-white/80 backdrop-blur-sm"
                    hx-get="{% url 'core:admin_resource_infographics_tab' %}"
                    hx-target="#resource-tab-content"
                    hx-include="[name='search'], [name='category']">
                <option value="">All Status</option>
                <option value="published" {% if current_status == 'published' %}selected{% endif %}>Published</option>
                <option value="draft" {% if current_status == 'draft' %}selected{% endif %}>Draft</option>
                <option value="featured" {% if current_status == 'featured' %}selected{% endif %}>Featured</option>
            </select>

            <!-- Category Filter -->
            <select name="category" 
                    class="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent bg-white/80 backdrop-blur-sm"
                    hx-get="{% url 'core:admin_resource_infographics_tab' %}"
                    hx-target="#resource-tab-content"
                    hx-include="[name='search'], [name='status']">
                <option value="">All Categories</option>
                {% for category in categories %}
                    <option value="{{ category.slug }}" {% if current_category == category.slug %}selected{% endif %}>{{ category.name }}</option>
                {% endfor %}
            </select>

            <!-- Create Infographic Button -->
            <button class="enhanced-btn enhanced-btn-primary text-sm"
                    hx-get="{% url 'core:admin_resource_create_modal' %}?content_type=infographic"
                    hx-target="body"
                    hx-swap="beforeend">
                <i class="fas fa-plus mr-2"></i>
                <span>New Infographic</span>
            </button>

            <!-- Refresh Button -->
            <button class="enhanced-btn enhanced-btn-secondary text-sm"
                    hx-get="{% url 'core:admin_resource_infographics_tab' %}"
                    hx-target="#resource-tab-content">
                <i class="fas fa-sync-alt mr-2"></i>
                <span>Refresh</span>
            </button>
        </div>
    </div>

    <!-- Infographics Grid View -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {% for infographic in infographics %}
        <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100">
            <!-- Image Preview -->
            <div class="relative h-48 bg-gradient-to-br from-yellow-100 to-orange-100">
                {% if infographic.featured_image %}
                    <img src="{{ infographic.featured_image.url }}" alt="{{ infographic.title }}" class="w-full h-full object-cover">
                {% else %}
                    <div class="w-full h-full flex items-center justify-center">
                        <i class="fas fa-chart-bar text-yellow-500 text-4xl"></i>
                    </div>
                {% endif %}
                
                <!-- Status Badges -->
                <div class="absolute top-2 left-2 flex flex-col space-y-1">
                    {% if infographic.is_published %}
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check-circle mr-1"></i>Published
                        </span>
                    {% else %}
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            <i class="fas fa-edit mr-1"></i>Draft
                        </span>
                    {% endif %}
                    {% if infographic.is_featured %}
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            <i class="fas fa-star mr-1"></i>Featured
                        </span>
                    {% endif %}
                </div>

                <!-- Selection Checkbox -->
                <div class="absolute top-2 right-2">
                    <input type="checkbox" class="infographic-checkbox rounded border-gray-300 text-harrier-red focus:ring-harrier-red" value="{{ infographic.id }}">
                </div>
            </div>

            <!-- Content -->
            <div class="p-4">
                <h3 class="font-semibold text-harrier-dark line-clamp-2 mb-2">{{ infographic.title }}</h3>
                {% if infographic.excerpt %}
                    <p class="text-sm text-gray-500 line-clamp-2 mb-3">{{ infographic.excerpt }}</p>
                {% endif %}

                <!-- Meta Information -->
                <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                    <div class="flex items-center space-x-2">
                        {% if infographic.author.profile_picture %}
                            <img src="{{ infographic.author.profile_picture.url }}" alt="{{ infographic.author.username }}" class="w-6 h-6 rounded-full">
                        {% else %}
                            <div class="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-gray-400 text-xs"></i>
                            </div>
                        {% endif %}
                        <span>{{ infographic.author.get_full_name|default:infographic.author.username }}</span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <i class="fas fa-eye text-gray-400"></i>
                        <span>{{ infographic.views_count|default:0 }}</span>
                    </div>
                </div>

                <!-- Category -->
                {% if infographic.category %}
                    <div class="mb-3">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            {{ infographic.category.name }}
                        </span>
                    </div>
                {% endif %}

                <!-- Updated Date -->
                <div class="text-xs text-gray-400 mb-4">
                    Updated: {{ infographic.updated_at|date:"M d, Y" }}
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <!-- Edit Button -->
                        <button class="enhanced-btn enhanced-btn-secondary text-xs px-3 py-1"
                                hx-get="{% url 'core:admin_resource_edit_modal' infographic.id %}"
                                hx-target="body"
                                hx-swap="beforeend"
                                title="Edit Infographic">
                            <i class="fas fa-edit"></i>
                        </button>

                        <!-- Toggle Published -->
                        <button class="enhanced-btn {% if infographic.is_published %}enhanced-btn-warning{% else %}enhanced-btn-success{% endif %} text-xs px-3 py-1"
                                hx-post="{% url 'core:admin_resource_toggle_published' infographic.id %}"
                                hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
                                hx-confirm="Are you sure you want to {% if infographic.is_published %}unpublish{% else %}publish{% endif %} this infographic?"
                                hx-target="#resource-tab-content"
                                hx-swap="outerHTML"
                                title="{% if infographic.is_published %}Unpublish{% else %}Publish{% endif %}">
                            <i class="fas fa-{% if infographic.is_published %}eye-slash{% else %}eye{% endif %}"></i>
                        </button>

                        <!-- Toggle Featured -->
                        <button class="enhanced-btn {% if infographic.is_featured %}enhanced-btn-warning{% else %}enhanced-btn-primary{% endif %} text-xs px-3 py-1"
                                hx-post="{% url 'core:admin_resource_toggle_featured' infographic.id %}"
                                hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
                                hx-target="#resource-tab-content"
                                hx-swap="outerHTML"
                                title="{% if infographic.is_featured %}Remove from Featured{% else %}Add to Featured{% endif %}">
                            <i class="fas fa-star"></i>
                        </button>
                    </div>

                    <!-- Delete Button -->
                    <button class="enhanced-btn enhanced-btn-danger text-xs px-3 py-1"
                            hx-post="{% url 'core:admin_resource_delete' infographic.id %}"
                            hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
                            hx-confirm="Are you sure you want to delete this infographic? This action cannot be undone."
                            hx-target="#resource-tab-content"
                            hx-swap="outerHTML"
                            title="Delete Infographic">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-span-full py-12 text-center">
            <div class="flex flex-col items-center">
                <i class="fas fa-chart-bar text-yellow-300 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-500 mb-2">No infographics found</h3>
                <p class="text-gray-400">{% if search_query %}No infographics match your search criteria.{% else %}Start by creating your first infographic.{% endif %}</p>
                {% if not search_query %}
                <button class="enhanced-btn enhanced-btn-primary mt-4"
                        hx-get="{% url 'core:admin_resource_create_modal' %}?content_type=infographic"
                        hx-target="body"
                        hx-swap="beforeend">
                    <i class="fas fa-plus mr-2"></i>Create Infographic
                </button>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Enhanced Pagination -->
    {% if infographics.has_other_pages %}
    <div class="enhanced-pagination-container mt-6">
        <div class="pagination-content">
            <div class="pagination-info">
                <div class="info-text">
                    <span class="showing-text">Showing</span>
                    <span class="range-numbers">{{ infographics.start_index }} - {{ infographics.end_index }}</span>
                    <span class="of-text">of</span>
                    <span class="total-number">{{ infographics.paginator.count }}</span>
                    <span class="items-text">infographics</span>
                </div>
                <div class="pagination-stats">
                    <span class="page-indicator">Page {{ infographics.number }} of {{ infographics.paginator.num_pages }}</span>
                </div>
            </div>

            <div class="pagination-controls">
                {% if infographics.has_previous %}
                    <a href="?page={{ infographics.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                       class="modern-pagination-btn"
                       hx-get="{% url 'core:admin_resource_infographics_tab' %}?page={{ infographics.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                       hx-target="#resource-tab-content">
                        <i class="fas fa-chevron-left mr-1"></i>
                        Previous
                    </a>
                {% endif %}

                <!-- Page numbers -->
                {% for num in infographics.paginator.page_range %}
                    {% if num == infographics.number %}
                        <span class="modern-pagination-btn active">{{ num }}</span>
                    {% elif num > infographics.number|add:'-3' and num < infographics.number|add:'3' %}
                        <a href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                           class="modern-pagination-btn"
                           hx-get="{% url 'core:admin_resource_infographics_tab' %}?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                           hx-target="#resource-tab-content">
                            {{ num }}
                        </a>
                    {% endif %}
                {% endfor %}

                {% if infographics.has_next %}
                    <a href="?page={{ infographics.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                       class="modern-pagination-btn"
                       hx-get="{% url 'core:admin_resource_infographics_tab' %}?page={{ infographics.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                       hx-target="#resource-tab-content">
                        Next
                        <i class="fas fa-chevron-right ml-1"></i>
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Bulk Actions Bar -->
    <div class="flex items-center justify-between mt-4 p-4 bg-gray-50 rounded-lg">
        <div class="flex items-center space-x-2">
            <input type="checkbox" id="select-all-infographics" class="rounded border-gray-300 text-harrier-red focus:ring-harrier-red">
            <label for="select-all-infographics" class="text-sm text-gray-700">Select All</label>
        </div>
        <div class="flex items-center space-x-2">
            <span id="selected-count" class="text-sm text-gray-600">0 selected</span>
            <button class="enhanced-btn enhanced-btn-secondary text-sm" disabled>
                <i class="fas fa-eye mr-2"></i>Bulk Publish
            </button>
            <button class="enhanced-btn enhanced-btn-danger text-sm" disabled>
                <i class="fas fa-trash mr-2"></i>Bulk Delete
            </button>
        </div>
    </div>
</div>

<script>
// Select all functionality for infographics
document.getElementById('select-all-infographics').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.infographic-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateSelectedCount();
});

// Individual checkbox handling for infographics
document.querySelectorAll('.infographic-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        updateSelectedCount();
    });
});

function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('.infographic-checkbox');
    const checkedBoxes = document.querySelectorAll('.infographic-checkbox:checked');
    const selectAll = document.getElementById('select-all-infographics');
    const selectedCount = document.getElementById('selected-count');
    
    selectAll.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes.length;
    selectAll.checked = checkedBoxes.length === checkboxes.length && checkboxes.length > 0;
    
    if (selectedCount) {
        selectedCount.textContent = `${checkedBoxes.length} selected`;
    }
}
</script>
