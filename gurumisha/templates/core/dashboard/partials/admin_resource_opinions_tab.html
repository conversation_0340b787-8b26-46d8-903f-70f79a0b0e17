{% load static %}

<div class="glassmorphism-card p-6">
    <!-- Enhanced Search and Filter Section -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
        <div class="flex-1 max-w-md">
            <div class="relative">
                <input type="text" 
                       id="opinions-search" 
                       placeholder="Search opinions..." 
                       value="{{ search_query }}"
                       class="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-300 bg-white/80 backdrop-blur-sm"
                       hx-get="{% url 'core:admin_resource_search' %}"
                       hx-target="#resource-tab-content"
                       hx-trigger="keyup changed delay:300ms"
                       hx-include="[name='status'], [name='category']"
                       hx-vals='{"tab": "opinions", "content_type": "opinion"}'>
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
            </div>
        </div>

        <div class="flex flex-wrap gap-3">
            <!-- Status Filter -->
            <select name="status" 
                    class="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent bg-white/80 backdrop-blur-sm"
                    hx-get="{% url 'core:admin_resource_opinions_tab' %}"
                    hx-target="#resource-tab-content"
                    hx-include="[name='search'], [name='category']">
                <option value="">All Status</option>
                <option value="published" {% if current_status == 'published' %}selected{% endif %}>Published</option>
                <option value="draft" {% if current_status == 'draft' %}selected{% endif %}>Draft</option>
                <option value="featured" {% if current_status == 'featured' %}selected{% endif %}>Featured</option>
            </select>

            <!-- Category Filter -->
            <select name="category" 
                    class="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent bg-white/80 backdrop-blur-sm"
                    hx-get="{% url 'core:admin_resource_opinions_tab' %}"
                    hx-target="#resource-tab-content"
                    hx-include="[name='search'], [name='status']">
                <option value="">All Categories</option>
                {% for category in categories %}
                    <option value="{{ category.slug }}" {% if current_category == category.slug %}selected{% endif %}>{{ category.name }}</option>
                {% endfor %}
            </select>

            <!-- Create Opinion Button -->
            <button class="enhanced-btn enhanced-btn-primary text-sm"
                    hx-get="{% url 'core:admin_resource_create_modal' %}?content_type=opinion"
                    hx-target="body"
                    hx-swap="beforeend">
                <i class="fas fa-plus mr-2"></i>
                <span>New Opinion</span>
            </button>

            <!-- Refresh Button -->
            <button class="enhanced-btn enhanced-btn-secondary text-sm"
                    hx-get="{% url 'core:admin_resource_opinions_tab' %}"
                    hx-target="#resource-tab-content">
                <i class="fas fa-sync-alt mr-2"></i>
                <span>Refresh</span>
            </button>
        </div>
    </div>

    <!-- Opinions List View -->
    <div class="space-y-4">
        {% for opinion in opinions %}
        <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 p-6">
            <div class="flex items-start justify-between">
                <!-- Content -->
                <div class="flex-1 min-w-0">
                    <div class="flex items-center space-x-3 mb-3">
                        <!-- Selection Checkbox -->
                        <input type="checkbox" class="opinion-checkbox rounded border-gray-300 text-harrier-red focus:ring-harrier-red" value="{{ opinion.id }}">
                        
                        <!-- Author Info -->
                        <div class="flex items-center space-x-2">
                            {% if opinion.author.profile_picture %}
                                <img src="{{ opinion.author.profile_picture.url }}" alt="{{ opinion.author.username }}" class="w-10 h-10 rounded-full">
                            {% else %}
                                <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-purple-500"></i>
                                </div>
                            {% endif %}
                            <div>
                                <h4 class="font-medium text-harrier-dark">{{ opinion.author.get_full_name|default:opinion.author.username }}</h4>
                                <p class="text-sm text-gray-500">{{ opinion.updated_at|date:"M d, Y" }}</p>
                            </div>
                        </div>

                        <!-- Status Badges -->
                        <div class="flex items-center space-x-2">
                            {% if opinion.is_published %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>Published
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-edit mr-1"></i>Draft
                                </span>
                            {% endif %}
                            {% if opinion.is_featured %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    <i class="fas fa-star mr-1"></i>Featured
                                </span>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Title and Content -->
                    <h3 class="text-xl font-semibold text-harrier-dark mb-2 line-clamp-2">{{ opinion.title }}</h3>
                    {% if opinion.excerpt %}
                        <p class="text-gray-600 line-clamp-3 mb-4">{{ opinion.excerpt }}</p>
                    {% endif %}

                    <!-- Meta Information -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4 text-sm text-gray-500">
                            {% if opinion.category %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    {{ opinion.category.name }}
                                </span>
                            {% endif %}
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-eye text-gray-400"></i>
                                <span>{{ opinion.views_count|default:0 }} views</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-clock text-gray-400"></i>
                                <span>{{ opinion.estimated_read_time }} min read</span>
                            </div>
                            {% if opinion.likes_count %}
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-heart text-red-400"></i>
                                <span>{{ opinion.likes_count }} likes</span>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex items-center space-x-2">
                            <!-- Edit Button -->
                            <button class="enhanced-btn enhanced-btn-secondary text-xs px-3 py-1"
                                    hx-get="{% url 'core:admin_resource_edit_modal' opinion.id %}"
                                    hx-target="body"
                                    hx-swap="beforeend"
                                    title="Edit Opinion">
                                <i class="fas fa-edit"></i>
                            </button>

                            <!-- Toggle Published -->
                            <button class="enhanced-btn {% if opinion.is_published %}enhanced-btn-warning{% else %}enhanced-btn-success{% endif %} text-xs px-3 py-1"
                                    hx-post="{% url 'core:admin_resource_toggle_published' opinion.id %}"
                                    hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
                                    hx-confirm="Are you sure you want to {% if opinion.is_published %}unpublish{% else %}publish{% endif %} this opinion?"
                                    hx-target="#resource-tab-content"
                                    hx-swap="outerHTML"
                                    title="{% if opinion.is_published %}Unpublish{% else %}Publish{% endif %}">
                                <i class="fas fa-{% if opinion.is_published %}eye-slash{% else %}eye{% endif %}"></i>
                            </button>

                            <!-- Toggle Featured -->
                            <button class="enhanced-btn {% if opinion.is_featured %}enhanced-btn-warning{% else %}enhanced-btn-primary{% endif %} text-xs px-3 py-1"
                                    hx-post="{% url 'core:admin_resource_toggle_featured' opinion.id %}"
                                    hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
                                    hx-target="#resource-tab-content"
                                    hx-swap="outerHTML"
                                    title="{% if opinion.is_featured %}Remove from Featured{% else %}Add to Featured{% endif %}">
                                <i class="fas fa-star"></i>
                            </button>

                            <!-- Delete Button -->
                            <button class="enhanced-btn enhanced-btn-danger text-xs px-3 py-1"
                                    hx-post="{% url 'core:admin_resource_delete' opinion.id %}"
                                    hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
                                    hx-confirm="Are you sure you want to delete this opinion? This action cannot be undone."
                                    hx-target="#resource-tab-content"
                                    hx-swap="outerHTML"
                                    title="Delete Opinion">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Featured Image -->
                {% if opinion.featured_image %}
                <div class="ml-6 flex-shrink-0">
                    <img src="{{ opinion.featured_image.url }}" alt="{{ opinion.title }}" class="w-24 h-24 rounded-lg object-cover">
                </div>
                {% endif %}
            </div>
        </div>
        {% empty %}
        <div class="py-12 text-center">
            <div class="flex flex-col items-center">
                <i class="fas fa-comment-alt text-purple-300 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-500 mb-2">No opinions found</h3>
                <p class="text-gray-400">{% if search_query %}No opinions match your search criteria.{% else %}Start by creating your first opinion piece.{% endif %}</p>
                {% if not search_query %}
                <button class="enhanced-btn enhanced-btn-primary mt-4"
                        hx-get="{% url 'core:admin_resource_create_modal' %}?content_type=opinion"
                        hx-target="body"
                        hx-swap="beforeend">
                    <i class="fas fa-plus mr-2"></i>Create Opinion
                </button>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Enhanced Pagination -->
    {% if opinions.has_other_pages %}
    <div class="enhanced-pagination-container mt-6">
        <div class="pagination-content">
            <div class="pagination-info">
                <div class="info-text">
                    <span class="showing-text">Showing</span>
                    <span class="range-numbers">{{ opinions.start_index }} - {{ opinions.end_index }}</span>
                    <span class="of-text">of</span>
                    <span class="total-number">{{ opinions.paginator.count }}</span>
                    <span class="items-text">opinions</span>
                </div>
                <div class="pagination-stats">
                    <span class="page-indicator">Page {{ opinions.number }} of {{ opinions.paginator.num_pages }}</span>
                </div>
            </div>

            <div class="pagination-controls">
                {% if opinions.has_previous %}
                    <a href="?page={{ opinions.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                       class="modern-pagination-btn"
                       hx-get="{% url 'core:admin_resource_opinions_tab' %}?page={{ opinions.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                       hx-target="#resource-tab-content">
                        <i class="fas fa-chevron-left mr-1"></i>
                        Previous
                    </a>
                {% endif %}

                <!-- Page numbers -->
                {% for num in opinions.paginator.page_range %}
                    {% if num == opinions.number %}
                        <span class="modern-pagination-btn active">{{ num }}</span>
                    {% elif num > opinions.number|add:'-3' and num < opinions.number|add:'3' %}
                        <a href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                           class="modern-pagination-btn"
                           hx-get="{% url 'core:admin_resource_opinions_tab' %}?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                           hx-target="#resource-tab-content">
                            {{ num }}
                        </a>
                    {% endif %}
                {% endfor %}

                {% if opinions.has_next %}
                    <a href="?page={{ opinions.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                       class="modern-pagination-btn"
                       hx-get="{% url 'core:admin_resource_opinions_tab' %}?page={{ opinions.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                       hx-target="#resource-tab-content">
                        Next
                        <i class="fas fa-chevron-right ml-1"></i>
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Bulk Actions Bar -->
    <div class="flex items-center justify-between mt-4 p-4 bg-gray-50 rounded-lg">
        <div class="flex items-center space-x-2">
            <input type="checkbox" id="select-all-opinions" class="rounded border-gray-300 text-harrier-red focus:ring-harrier-red">
            <label for="select-all-opinions" class="text-sm text-gray-700">Select All</label>
        </div>
        <div class="flex items-center space-x-2">
            <span id="selected-count-opinions" class="text-sm text-gray-600">0 selected</span>
            <button class="enhanced-btn enhanced-btn-secondary text-sm" disabled>
                <i class="fas fa-eye mr-2"></i>Bulk Publish
            </button>
            <button class="enhanced-btn enhanced-btn-danger text-sm" disabled>
                <i class="fas fa-trash mr-2"></i>Bulk Delete
            </button>
        </div>
    </div>
</div>

<script>
// Select all functionality for opinions
document.getElementById('select-all-opinions').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.opinion-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateSelectedCountOpinions();
});

// Individual checkbox handling for opinions
document.querySelectorAll('.opinion-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        updateSelectedCountOpinions();
    });
});

function updateSelectedCountOpinions() {
    const checkboxes = document.querySelectorAll('.opinion-checkbox');
    const checkedBoxes = document.querySelectorAll('.opinion-checkbox:checked');
    const selectAll = document.getElementById('select-all-opinions');
    const selectedCount = document.getElementById('selected-count-opinions');
    
    selectAll.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes.length;
    selectAll.checked = checkedBoxes.length === checkboxes.length && checkboxes.length > 0;
    
    if (selectedCount) {
        selectedCount.textContent = `${checkedBoxes.length} selected`;
    }
}
</script>
