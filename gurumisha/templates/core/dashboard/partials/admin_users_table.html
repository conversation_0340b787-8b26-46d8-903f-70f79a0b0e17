<!-- Enhanced Admin Users Table Partial for HTMX Updates -->
<div class="table-container">
    <table class="min-w-full divide-y divide-gray-200 modern-admin-table">
        <thead class="bg-gradient-to-r from-gray-50 to-gray-100">
            <tr>
                <th class="px-4 py-4 text-left w-12">
                    <input
                        type="checkbox"
                        id="selectAll"
                        class="h-4 w-4 text-harrier-red focus:ring-harrier-red border-gray-300 rounded transition-all duration-200"
                        onchange="selectAllUsers()"
                        aria-label="Select all users">
                </th>
                <th class="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider font-montserrat min-w-[200px]">
                    <div class="flex items-center">
                        <i class="fas fa-user mr-2 text-harrier-red"></i>
                        User
                    </div>
                </th>
                <th class="px-4 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider font-montserrat">
                    <div class="flex items-center">
                        <i class="fas fa-tag mr-2 text-harrier-red"></i>
                        Role
                    </div>
                </th>
                <th class="px-4 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider font-montserrat">
                    <div class="flex items-center">
                        <i class="fas fa-circle mr-2 text-harrier-red"></i>
                        Status
                    </div>
                </th>
                <th class="px-4 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider font-montserrat hidden md:table-cell">
                    <div class="flex items-center">
                        <i class="fas fa-calendar mr-2 text-harrier-red"></i>
                        Joined
                    </div>
                </th>
                <th class="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider font-montserrat min-w-[280px]">
                    <div class="flex items-center">
                        <i class="fas fa-cogs mr-2 text-harrier-red"></i>
                        Actions
                    </div>
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-100">
            {% for user in users %}
                <tr id="user-{{ user.id }}" class="modern-table-row hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 transition-all duration-300 group">
                    <td class="px-4 py-4 whitespace-nowrap">
                        <input
                            type="checkbox"
                            name="selected_users"
                            value="{{ user.id }}"
                            class="h-4 w-4 text-harrier-red focus:ring-harrier-red border-gray-300 rounded transition-all duration-200"
                            onchange="updateBulkActions()"
                            aria-label="Select user {{ user.username }}">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gradient-to-br from-harrier-red to-harrier-dark rounded-full flex items-center justify-center text-white font-bold text-sm mr-4 shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                                {{ user.first_name|first|default:user.username|first|upper }}
                            </div>
                            <div class="min-w-0 flex-1 user-info">
                                <div class="text-sm font-semibold text-gray-900 font-raleway group-hover:text-harrier-red transition-colors duration-300 truncate">
                                    <a href="{% url 'core:admin_user_detail' user.id %}" class="hover:text-harrier-red transition-colors" onclick="event.stopPropagation();">
                                        {{ user.first_name|default:user.username }} {{ user.last_name }}
                                    </a>
                                </div>
                                <div class="text-sm text-gray-500 font-raleway truncate">{{ user.email }}</div>
                                {% if user.phone %}
                                    <div class="text-xs text-gray-400 font-raleway truncate">{{ user.phone }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold font-montserrat
                            {% if user.role == 'admin' %}bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800 border border-purple-300
                            {% elif user.role == 'vendor' %}bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 border border-blue-300
                            {% else %}bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 border border-gray-300{% endif %}
                            shadow-sm hover:shadow-md transition-all duration-200">
                            <i class="fas fa-{% if user.role == 'admin' %}crown{% elif user.role == 'vendor' %}store{% else %}user{% endif %} mr-1"></i>
                            {{ user.get_role_display }}
                        </span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="flex flex-col space-y-1">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold font-montserrat
                                {% if user.is_active %}bg-gradient-to-r from-green-100 to-green-200 text-green-800 border border-green-300
                                {% else %}bg-gradient-to-r from-red-100 to-red-200 text-red-800 border border-red-300{% endif %}
                                shadow-sm hover:shadow-md transition-all duration-200">
                                <i class="fas fa-{% if user.is_active %}check-circle{% else %}times-circle{% endif %} mr-1"></i>
                                {% if user.is_active %}Active{% else %}Inactive{% endif %}
                            </span>
                            {% if user.is_verified %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 border border-blue-300 shadow-sm hover:shadow-md transition-all duration-200 font-montserrat">
                                    <i class="fas fa-shield-check mr-1"></i>
                                    Verified
                                </span>
                            {% endif %}
                        </div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900 font-raleway hidden md:table-cell">
                        <div class="flex flex-col">
                            <span class="font-medium">{{ user.date_joined|date:"M d, Y" }}</span>
                            <span class="text-xs text-gray-500">{{ user.date_joined|date:"H:i" }}</span>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <!-- Desktop Action Buttons -->
                        <div class="hidden lg:flex items-center space-x-2 relative z-10">
                            <!-- Profile Button -->
                            <a
                                href="{% url 'core:admin_user_detail' user.id %}"
                                class="modern-action-btn profile-btn"
                                title="View User Profile"
                                aria-label="View profile for {{ user.username }}"
                                onclick="event.stopPropagation();">
                                <i class="fas fa-user mr-1"></i>
                                <span class="hidden xl:inline">Profile</span>
                            </a>

                            <!-- Vendor Profile Button (only for vendors) -->
                            {% if user.role == 'vendor' %}
                                <a
                                    href="{% url 'core:admin_vendor_user_detail' user.id %}"
                                    class="modern-action-btn vendor-btn"
                                    title="View Vendor Profile"
                                    aria-label="View vendor profile for {{ user.username }}"
                                    onclick="event.stopPropagation();">
                                    <i class="fas fa-store mr-1"></i>
                                    <span class="hidden xl:inline">Vendor</span>
                                </a>
                            {% endif %}

                            <!-- Activate/Deactivate Button -->
                            <button
                                type="button"
                                class="modern-action-btn {% if user.is_active %}deactivate-btn{% else %}activate-btn{% endif %}"
                                onclick="event.stopPropagation(); toggleUserStatus({{ user.id }}, '{% if user.is_active %}deactivate{% else %}activate{% endif %}')"
                                title="{% if user.is_active %}Deactivate User{% else %}Activate User{% endif %}"
                                aria-label="{% if user.is_active %}Deactivate{% else %}Activate{% endif %} {{ user.username }}">
                                <i class="fas fa-{% if user.is_active %}user-slash{% else %}user-check{% endif %} mr-1"></i>
                                <span class="hidden xl:inline">{% if user.is_active %}Deactivate{% else %}Activate{% endif %}</span>
                            </button>

                            <!-- Delete Button -->
                            <button
                                type="button"
                                class="modern-action-btn delete-btn"
                                onclick="event.stopPropagation(); deleteUser({{ user.id }})"
                                title="Delete User"
                                aria-label="Delete {{ user.username }}">
                                <i class="fas fa-trash mr-1"></i>
                                <span class="hidden xl:inline">Delete</span>
                            </button>
                        </div>

                        <!-- Mobile/Tablet Dropdown Menu -->
                        <div class="lg:hidden relative z-20">
                            <button
                                type="button"
                                class="modern-action-btn profile-btn dropdown-toggle"
                                onclick="event.stopPropagation(); toggleDropdown({{ user.id }})"
                                title="User Actions"
                                aria-label="Actions for {{ user.username }}"
                                aria-expanded="false"
                                aria-haspopup="true">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>

                            <div id="dropdown-{{ user.id }}" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                                <div class="py-2">
                                    <a
                                        href="{% url 'core:admin_user_detail' user.id %}"
                                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                                        onclick="event.stopPropagation();">
                                        <i class="fas fa-user mr-3 text-blue-500"></i>
                                        View Profile
                                    </a>

                                    {% if user.role == 'vendor' %}
                                        <a
                                            href="{% url 'core:admin_vendor_user_detail' user.id %}"
                                            class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                                            onclick="event.stopPropagation();">
                                            <i class="fas fa-store mr-3 text-green-500"></i>
                                            Vendor Profile
                                        </a>
                                    {% endif %}

                                    <button
                                        type="button"
                                        class="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors text-left"
                                        onclick="event.stopPropagation(); toggleUserStatus({{ user.id }}, '{% if user.is_active %}deactivate{% else %}activate{% endif %}'); toggleDropdown({{ user.id }})">
                                        <i class="fas fa-{% if user.is_active %}user-slash{% else %}user-check{% endif %} mr-3 {% if user.is_active %}text-yellow-500{% else %}text-green-500{% endif %}"></i>
                                        {% if user.is_active %}Deactivate{% else %}Activate{% endif %}
                                    </button>

                                    <button
                                        type="button"
                                        class="w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors text-left"
                                        onclick="event.stopPropagation(); deleteUser({{ user.id }}); toggleDropdown({{ user.id }})">
                                        <i class="fas fa-trash mr-3 text-red-500"></i>
                                        Delete User
                                    </button>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            {% empty %}
                <tr>
                    <td colspan="6" class="px-6 py-16 text-center">
                        <div class="flex flex-col items-center justify-center space-y-6">
                            <div class="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center shadow-lg">
                                <i class="fas fa-users text-3xl text-gray-400"></i>
                            </div>
                            <div class="text-gray-500 font-raleway max-w-md">
                                <p class="text-xl font-semibold mb-2">No users found</p>
                                <p class="text-sm text-gray-400">Try adjusting your search criteria or filters to find users</p>
                            </div>
                            <button
                                type="button"
                                class="modern-action-btn profile-btn"
                                onclick="clearFilters()"
                                title="Clear all filters">
                                <i class="fas fa-filter mr-2"></i>
                                Clear Filters
                            </button>
                        </div>
                    </td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Enhanced Pagination -->
{% if users.has_other_pages %}
    <div class="px-6 py-4 border-t border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700 font-raleway">
                <span class="font-medium">Showing {{ users.start_index }} to {{ users.end_index }}</span>
                of <span class="font-medium">{{ users.paginator.count }}</span> results
            </div>
            <div class="flex items-center space-x-2">
                {% if users.has_previous %}
                    <a href="?page={{ users.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_role_filter %}&role={{ current_role_filter }}{% endif %}{% if current_status_filter %}&status={{ current_status_filter }}{% endif %}"
                       class="modern-pagination-btn">
                        <i class="fas fa-chevron-left mr-1"></i>
                        Previous
                    </a>
                {% endif %}

                <!-- Page numbers -->
                {% for num in users.paginator.page_range %}
                    {% if num == users.number %}
                        <span class="modern-pagination-btn active">{{ num }}</span>
                    {% elif num > users.number|add:'-3' and num < users.number|add:'3' %}
                        <a href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_role_filter %}&role={{ current_role_filter }}{% endif %}{% if current_status_filter %}&status={{ current_status_filter }}{% endif %}"
                           class="modern-pagination-btn">
                            {{ num }}
                        </a>
                    {% endif %}
                {% endfor %}

                {% if users.has_next %}
                    <a href="?page={{ users.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_role_filter %}&role={{ current_role_filter }}{% endif %}{% if current_status_filter %}&status={{ current_status_filter }}{% endif %}"
                       class="modern-pagination-btn">
                        Next
                        <i class="fas fa-chevron-right ml-1"></i>
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
{% endif %}
