{% load static %}

<div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-raleway">
                    Vehicle Details
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-raleway">
                    Budget Range
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-raleway">
                    Status
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-raleway">
                    Progress
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-raleway">
                    Date
                </th>
                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider font-raleway">
                    Actions
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for request in import_requests %}
            <tr class="hover:bg-gray-50 transition-colors duration-200">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-12 w-12">
                            <div class="h-12 w-12 rounded-lg bg-gradient-to-br from-harrier-red to-harrier-red-dark flex items-center justify-center">
                                <i class="fas fa-car text-white text-lg"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900 font-montserrat">
                                {{ request.year }} {{ request.brand }} {{ request.model }}
                            </div>
                            <div class="text-sm text-gray-500 font-raleway">
                                <i class="fas fa-map-marker-alt mr-1"></i>{{ request.origin_country }}
                                {% if request.preferred_color %}
                                    <span class="ml-2">
                                        <i class="fas fa-palette mr-1"></i>{{ request.preferred_color }}
                                    </span>
                                {% endif %}
                            </div>
                            {% if request.special_requirements %}
                                <div class="text-xs text-gray-400 mt-1 font-raleway">
                                    <i class="fas fa-info-circle mr-1"></i>Special requirements
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900 font-montserrat">
                        KSh {{ request.budget_min|floatformat:0 }} - {{ request.budget_max|floatformat:0 }}
                    </div>
                    {% if request.estimated_cost %}
                        <div class="text-sm text-green-600 font-raleway">
                            <i class="fas fa-calculator mr-1"></i>Est: KSh {{ request.estimated_cost|floatformat:0 }}
                        </div>
                    {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    {% if request.status == 'pending' %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            <i class="fas fa-clock mr-1"></i>Pending
                        </span>
                    {% elif request.status == 'on_quotation' %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            <i class="fas fa-file-invoice mr-1"></i>On Quotation
                        </span>
                    {% elif request.status == 'processing' %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            <i class="fas fa-cogs mr-1"></i>Processing
                        </span>
                    {% elif request.status == 'fee_paid' %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                            <i class="fas fa-credit-card mr-1"></i>Fee Paid
                        </span>
                    {% elif request.status == 'completed' %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check-circle mr-1"></i>Completed
                        </span>
                    {% elif request.status == 'cancelled' %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <i class="fas fa-times-circle mr-1"></i>Cancelled
                        </span>
                    {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        {% if request.status == 'pending' %}
                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 20%"></div>
                        {% elif request.status == 'on_quotation' %}
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 40%"></div>
                        {% elif request.status == 'processing' %}
                            <div class="bg-purple-500 h-2 rounded-full" style="width: 60%"></div>
                        {% elif request.status == 'fee_paid' %}
                            <div class="bg-indigo-500 h-2 rounded-full" style="width: 80%"></div>
                        {% elif request.status == 'completed' %}
                            <div class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
                        {% elif request.status == 'cancelled' %}
                            <div class="bg-red-500 h-2 rounded-full" style="width: 0%"></div>
                        {% endif %}
                    </div>
                    <div class="text-xs text-gray-500 mt-1 font-raleway">
                        {% if request.status == 'pending' %}
                            Awaiting quotation
                        {% elif request.status == 'on_quotation' %}
                            Quotation provided
                        {% elif request.status == 'processing' %}
                            Being processed
                        {% elif request.status == 'fee_paid' %}
                            Payment confirmed
                        {% elif request.status == 'completed' %}
                            Import completed
                        {% elif request.status == 'cancelled' %}
                            Request cancelled
                        {% endif %}
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-raleway">
                    <div>{{ request.created_at|date:"M d, Y" }}</div>
                    <div class="text-xs">{{ request.created_at|time:"H:i" }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                    <div class="flex items-center justify-center space-x-2">
                        <!-- View Details Button -->
                        <a href="{% url 'core:vendor_import_request_detail' request.id %}"
                           class="inline-flex items-center px-3 py-1.5 bg-harrier-red text-white text-xs font-medium rounded-lg hover:bg-harrier-red-dark transition-all duration-200 transform hover:scale-105 shadow-sm hover:shadow-md">
                            <i class="fas fa-eye mr-1"></i>
                            View
                        </a>

                        {% if request.status == 'on_quotation' or request.status == 'processing' or request.status == 'fee_paid' %}
                        <!-- Track Button for active requests -->
                        <a href="{% url 'core:vendor_import_request_detail' request.id %}"
                           class="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 transition-all duration-200 transform hover:scale-105 shadow-sm hover:shadow-md">
                            <i class="fas fa-shipping-fast mr-1"></i>
                            Track
                        </a>
                        {% endif %}
                    </div>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="px-6 py-12 text-center">
                    <div class="flex flex-col items-center justify-center">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-file-import text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 font-montserrat mb-2">No Import Requests Found</h3>
                        <p class="text-gray-500 font-raleway mb-4">
                            {% if search_query or status_filter %}
                                No import requests match your current filters.
                            {% else %}
                                You haven't submitted any import requests yet.
                            {% endif %}
                        </p>
                        {% if not search_query and not status_filter %}
                        <a href="{% url 'core:import_request' %}"
                           class="inline-flex items-center px-4 py-2 bg-harrier-red text-white text-sm font-medium rounded-lg hover:bg-harrier-red-dark transition-all duration-200 transform hover:scale-105 shadow-sm hover:shadow-md">
                            <i class="fas fa-plus mr-2"></i>
                            Submit Import Request
                        </a>
                        {% endif %}
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Pagination -->
{% if is_paginated %}
<div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
    <div class="flex-1 flex justify-between sm:hidden">
        {% if page_obj.has_previous %}
            <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
               class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Previous
            </a>
        {% endif %}
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
               class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Next
            </a>
        {% endif %}
    </div>
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
            <p class="text-sm text-gray-700 font-raleway">
                Showing
                <span class="font-medium">{{ page_obj.start_index }}</span>
                to
                <span class="font-medium">{{ page_obj.end_index }}</span>
                of
                <span class="font-medium">{{ page_obj.paginator.count }}</span>
                results
            </p>
        </div>
        <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
                       class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-harrier-red bg-harrier-red text-sm font-medium text-white">
                            {{ num }}
                        </span>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            {{ num }}
                        </a>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                {% endif %}
            </nav>
        </div>
    </div>
</div>
{% endif %}


