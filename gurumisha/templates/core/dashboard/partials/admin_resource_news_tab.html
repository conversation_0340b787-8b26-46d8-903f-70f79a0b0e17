{% load static %}

<div class="glassmorphism-card p-6">
    <!-- Enhanced Search and Filter Section -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
        <div class="flex-1 max-w-md">
            <div class="relative">
                <input type="text" 
                       id="news-search" 
                       placeholder="Search news..." 
                       value="{{ search_query }}"
                       class="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-300 bg-white/80 backdrop-blur-sm"
                       hx-get="{% url 'core:admin_resource_search' %}"
                       hx-target="#resource-tab-content"
                       hx-trigger="keyup changed delay:300ms"
                       hx-include="[name='status'], [name='category']"
                       hx-vals='{"tab": "news", "content_type": "news"}'>
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
            </div>
        </div>

        <div class="flex flex-wrap gap-3">
            <!-- Status Filter -->
            <select name="status" 
                    class="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent bg-white/80 backdrop-blur-sm"
                    hx-get="{% url 'core:admin_resource_news_tab' %}"
                    hx-target="#resource-tab-content"
                    hx-include="[name='search'], [name='category']">
                <option value="">All Status</option>
                <option value="published" {% if current_status == 'published' %}selected{% endif %}>Published</option>
                <option value="draft" {% if current_status == 'draft' %}selected{% endif %}>Draft</option>
                <option value="featured" {% if current_status == 'featured' %}selected{% endif %}>Featured</option>
            </select>

            <!-- Category Filter -->
            <select name="category" 
                    class="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent bg-white/80 backdrop-blur-sm"
                    hx-get="{% url 'core:admin_resource_news_tab' %}"
                    hx-target="#resource-tab-content"
                    hx-include="[name='search'], [name='status']">
                <option value="">All Categories</option>
                {% for category in categories %}
                    <option value="{{ category.slug }}" {% if current_category == category.slug %}selected{% endif %}>{{ category.name }}</option>
                {% endfor %}
            </select>

            <!-- Create News Button -->
            <button class="enhanced-btn enhanced-btn-primary text-sm"
                    hx-get="{% url 'core:admin_resource_create_modal' %}?content_type=news"
                    hx-target="body"
                    hx-swap="beforeend">
                <i class="fas fa-plus mr-2"></i>
                <span>New News</span>
            </button>

            <!-- Refresh Button -->
            <button class="enhanced-btn enhanced-btn-secondary text-sm"
                    hx-get="{% url 'core:admin_resource_news_tab' %}"
                    hx-target="#resource-tab-content">
                <i class="fas fa-sync-alt mr-2"></i>
                <span>Refresh</span>
            </button>
        </div>
    </div>

    <!-- News Table -->
    <div class="overflow-x-auto">
        <table class="w-full">
            <thead>
                <tr class="border-b border-gray-200">
                    <th class="text-left py-4 px-4 font-semibold text-harrier-dark">
                        <input type="checkbox" id="select-all-news" class="rounded border-gray-300 text-harrier-red focus:ring-harrier-red">
                    </th>
                    <th class="text-left py-4 px-4 font-semibold text-harrier-dark">News Article</th>
                    <th class="text-left py-4 px-4 font-semibold text-harrier-dark">Author</th>
                    <th class="text-left py-4 px-4 font-semibold text-harrier-dark">Category</th>
                    <th class="text-left py-4 px-4 font-semibold text-harrier-dark">Status</th>
                    <th class="text-left py-4 px-4 font-semibold text-harrier-dark">Views</th>
                    <th class="text-left py-4 px-4 font-semibold text-harrier-dark">Published</th>
                    <th class="text-left py-4 px-4 font-semibold text-harrier-dark">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for news_item in news %}
                <tr class="border-b border-gray-100 hover:bg-gray-50/50 transition-colors">
                    <td class="py-4 px-4">
                        <input type="checkbox" class="news-checkbox rounded border-gray-300 text-harrier-red focus:ring-harrier-red" value="{{ news_item.id }}">
                    </td>
                    <td class="py-4 px-4">
                        <div class="flex items-start space-x-3">
                            {% if news_item.featured_image %}
                                <img src="{{ news_item.featured_image.url }}" alt="{{ news_item.title }}" class="w-16 h-12 rounded-lg object-cover">
                            {% else %}
                                <div class="w-16 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-rss text-red-500"></i>
                                </div>
                            {% endif %}
                            <div class="flex-1 min-w-0">
                                <h3 class="font-medium text-harrier-dark line-clamp-2">{{ news_item.title }}</h3>
                                {% if news_item.excerpt %}
                                    <p class="text-sm text-gray-500 line-clamp-2 mt-1">{{ news_item.excerpt }}</p>
                                {% endif %}
                                <div class="flex items-center space-x-2 mt-2">
                                    {% if news_item.video_url %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-video mr-1"></i>Video
                                        </span>
                                    {% endif %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        <i class="fas fa-clock mr-1"></i>{{ news_item.estimated_read_time }} min
                                    </span>
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <div class="flex items-center space-x-2">
                            {% if news_item.author.profile_picture %}
                                <img src="{{ news_item.author.profile_picture.url }}" alt="{{ news_item.author.username }}" class="w-8 h-8 rounded-full">
                            {% else %}
                                <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-gray-400 text-xs"></i>
                                </div>
                            {% endif %}
                            <span class="text-sm text-gray-700">{{ news_item.author.get_full_name|default:news_item.author.username }}</span>
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        {% if news_item.category %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                {{ news_item.category.name }}
                            </span>
                        {% else %}
                            <span class="text-sm text-gray-400">Uncategorized</span>
                        {% endif %}
                    </td>
                    <td class="py-4 px-4">
                        <div class="flex flex-col space-y-1">
                            {% if news_item.is_published %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>Published
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-edit mr-1"></i>Draft
                                </span>
                            {% endif %}
                            {% if news_item.is_featured %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    <i class="fas fa-star mr-1"></i>Featured
                                </span>
                            {% endif %}
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-eye text-gray-400 text-xs"></i>
                            <span class="text-sm text-gray-700">{{ news_item.views_count|default:0 }}</span>
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        {% if news_item.published_at %}
                            <span class="text-sm text-gray-700">{{ news_item.published_at|date:"M d, Y" }}</span>
                            <br><span class="text-xs text-gray-400">{{ news_item.published_at|timesince }} ago</span>
                        {% else %}
                            <span class="text-sm text-gray-400">Not published</span>
                        {% endif %}
                    </td>
                    <td class="py-4 px-4">
                        <div class="flex items-center space-x-2">
                            <!-- Edit Button -->
                            <button class="enhanced-btn enhanced-btn-secondary text-xs px-3 py-1"
                                    hx-get="{% url 'core:admin_resource_edit_modal' news_item.id %}"
                                    hx-target="body"
                                    hx-swap="beforeend"
                                    title="Edit News">
                                <i class="fas fa-edit"></i>
                            </button>

                            <!-- Toggle Published -->
                            <button class="enhanced-btn {% if news_item.is_published %}enhanced-btn-warning{% else %}enhanced-btn-success{% endif %} text-xs px-3 py-1"
                                    hx-post="{% url 'core:admin_resource_toggle_published' news_item.id %}"
                                    hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
                                    hx-confirm="Are you sure you want to {% if news_item.is_published %}unpublish{% else %}publish{% endif %} this news article?"
                                    hx-target="#resource-tab-content"
                                    hx-swap="outerHTML"
                                    title="{% if news_item.is_published %}Unpublish{% else %}Publish{% endif %}">
                                <i class="fas fa-{% if news_item.is_published %}eye-slash{% else %}eye{% endif %}"></i>
                            </button>

                            <!-- Toggle Featured -->
                            <button class="enhanced-btn {% if news_item.is_featured %}enhanced-btn-warning{% else %}enhanced-btn-primary{% endif %} text-xs px-3 py-1"
                                    hx-post="{% url 'core:admin_resource_toggle_featured' news_item.id %}"
                                    hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
                                    hx-target="#resource-tab-content"
                                    hx-swap="outerHTML"
                                    title="{% if news_item.is_featured %}Remove from Featured{% else %}Add to Featured{% endif %}">
                                <i class="fas fa-star"></i>
                            </button>

                            <!-- Delete Button -->
                            <button class="enhanced-btn enhanced-btn-danger text-xs px-3 py-1"
                                    hx-post="{% url 'core:admin_resource_delete' news_item.id %}"
                                    hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
                                    hx-confirm="Are you sure you want to delete this news article? This action cannot be undone."
                                    hx-target="#resource-tab-content"
                                    hx-swap="outerHTML"
                                    title="Delete News">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="8" class="py-12 text-center">
                        <div class="flex flex-col items-center">
                            <i class="fas fa-rss text-red-300 text-4xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-500 mb-2">No news found</h3>
                            <p class="text-gray-400">{% if search_query %}No news articles match your search criteria.{% else %}Start by creating your first news article.{% endif %}</p>
                            {% if not search_query %}
                            <button class="enhanced-btn enhanced-btn-primary mt-4"
                                    hx-get="{% url 'core:admin_resource_create_modal' %}?content_type=news"
                                    hx-target="body"
                                    hx-swap="beforeend">
                                <i class="fas fa-plus mr-2"></i>Create News
                            </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Enhanced Pagination -->
    {% if news.has_other_pages %}
    <div class="enhanced-pagination-container mt-6">
        <div class="pagination-content">
            <div class="pagination-info">
                <div class="info-text">
                    <span class="showing-text">Showing</span>
                    <span class="range-numbers">{{ news.start_index }} - {{ news.end_index }}</span>
                    <span class="of-text">of</span>
                    <span class="total-number">{{ news.paginator.count }}</span>
                    <span class="items-text">news articles</span>
                </div>
                <div class="pagination-stats">
                    <span class="page-indicator">Page {{ news.number }} of {{ news.paginator.num_pages }}</span>
                </div>
            </div>

            <div class="pagination-controls">
                {% if news.has_previous %}
                    <a href="?page={{ news.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                       class="modern-pagination-btn"
                       hx-get="{% url 'core:admin_resource_news_tab' %}?page={{ news.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                       hx-target="#resource-tab-content">
                        <i class="fas fa-chevron-left mr-1"></i>
                        Previous
                    </a>
                {% endif %}

                <!-- Page numbers -->
                {% for num in news.paginator.page_range %}
                    {% if num == news.number %}
                        <span class="modern-pagination-btn active">{{ num }}</span>
                    {% elif num > news.number|add:'-3' and num < news.number|add:'3' %}
                        <a href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                           class="modern-pagination-btn"
                           hx-get="{% url 'core:admin_resource_news_tab' %}?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                           hx-target="#resource-tab-content">
                            {{ num }}
                        </a>
                    {% endif %}
                {% endfor %}

                {% if news.has_next %}
                    <a href="?page={{ news.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                       class="modern-pagination-btn"
                       hx-get="{% url 'core:admin_resource_news_tab' %}?page={{ news.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                       hx-target="#resource-tab-content">
                        Next
                        <i class="fas fa-chevron-right ml-1"></i>
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
// Select all functionality for news
document.getElementById('select-all-news').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.news-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Individual checkbox handling for news
document.querySelectorAll('.news-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        const selectAll = document.getElementById('select-all-news');
        const checkboxes = document.querySelectorAll('.news-checkbox');
        const checkedBoxes = document.querySelectorAll('.news-checkbox:checked');
        
        selectAll.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes.length;
        selectAll.checked = checkedBoxes.length === checkboxes.length;
    });
});
</script>
