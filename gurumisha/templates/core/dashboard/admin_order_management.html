{% extends 'base_admin_dashboard.html' %}
{% load static %}

{% block page_title %}Order Management{% endblock %}
{% block page_description %}Comprehensive order tracking and management system for spare parts{% endblock %}

{% block dashboard_content %}
<div class="space-y-8">
    <!-- Enhanced Header -->
    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 animate-fade-in-up" style="animation-delay: 0.1s;">
        <div>
            <h2 class="text-3xl font-bold text-harrier-dark font-montserrat">Order Management</h2>
            <p class="text-gray-600 mt-1 font-raleway">Track and manage customer orders with real-time updates</p>
        </div>
        
        <div class="flex flex-wrap gap-3">
            <button onclick="exportOrderData()" class="enhanced-btn enhanced-btn-secondary text-sm">
                <i class="fas fa-download mr-2"></i>Export Orders
            </button>
            <button onclick="bulkOrderActions()" class="enhanced-btn enhanced-btn-secondary text-sm">
                <i class="fas fa-tasks mr-2"></i>Bulk Actions
            </button>
            <button onclick="createManualOrder()" class="enhanced-btn enhanced-btn-primary text-sm">
                <i class="fas fa-plus mr-2"></i>Create Order
            </button>
        </div>
    </div>

    <!-- Order Status Overview -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 animate-fade-in-up" style="animation-delay: 0.2s;">
        <div class="glassmorphism-card stat-card-enhanced group hover:scale-105 transition-all duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <div class="stat-value text-blue-600 font-montserrat">45</div>
                    <div class="stat-label font-raleway">Pending</div>
                    <div class="stat-trend text-xs text-blue-600 mt-1">
                        <i class="fas fa-clock mr-1"></i>Awaiting processing
                    </div>
                </div>
                <div class="stat-icon bg-gradient-to-br from-blue-500 to-blue-600">
                    <i class="fas fa-clock text-white"></i>
                </div>
            </div>
        </div>
        
        <div class="glassmorphism-card stat-card-enhanced group hover:scale-105 transition-all duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <div class="stat-value text-orange-600 font-montserrat">23</div>
                    <div class="stat-label font-raleway">Processing</div>
                    <div class="stat-trend text-xs text-orange-600 mt-1">
                        <i class="fas fa-cogs mr-1"></i>In progress
                    </div>
                </div>
                <div class="stat-icon bg-gradient-to-br from-orange-500 to-orange-600">
                    <i class="fas fa-cogs text-white"></i>
                </div>
            </div>
        </div>
        
        <div class="glassmorphism-card stat-card-enhanced group hover:scale-105 transition-all duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <div class="stat-value text-purple-600 font-montserrat">67</div>
                    <div class="stat-label font-raleway">Shipped</div>
                    <div class="stat-trend text-xs text-purple-600 mt-1">
                        <i class="fas fa-shipping-fast mr-1"></i>In transit
                    </div>
                </div>
                <div class="stat-icon bg-gradient-to-br from-purple-500 to-purple-600">
                    <i class="fas fa-shipping-fast text-white"></i>
                </div>
            </div>
        </div>
        
        <div class="glassmorphism-card stat-card-enhanced group hover:scale-105 transition-all duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <div class="stat-value text-green-600 font-montserrat">189</div>
                    <div class="stat-label font-raleway">Delivered</div>
                    <div class="stat-trend text-xs text-green-600 mt-1">
                        <i class="fas fa-check-circle mr-1"></i>Completed
                    </div>
                </div>
                <div class="stat-icon bg-gradient-to-br from-green-500 to-green-600">
                    <i class="fas fa-check-circle text-white"></i>
                </div>
            </div>
        </div>
        
        <div class="glassmorphism-card stat-card-enhanced group hover:scale-105 transition-all duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <div class="stat-value text-red-600 font-montserrat">8</div>
                    <div class="stat-label font-raleway">Cancelled</div>
                    <div class="stat-trend text-xs text-red-600 mt-1">
                        <i class="fas fa-times-circle mr-1"></i>Refunded
                    </div>
                </div>
                <div class="stat-icon bg-gradient-to-br from-red-500 to-red-600">
                    <i class="fas fa-times-circle text-white"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Filters and Search -->
    <div class="glassmorphism-card animate-fade-in-up" style="animation-delay: 0.3s;">
        <div class="p-6">
            <div class="flex flex-col lg:flex-row gap-4">
                <!-- Search Bar -->
                <div class="flex-1">
                    <div class="relative">
                        <input type="text" 
                               id="order-search"
                               placeholder="Search orders by ID, customer, or product..."
                               class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-transparent font-raleway text-sm bg-white shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="flex flex-wrap gap-3">
                    <select class="enhanced-select">
                        <option>All Status</option>
                        <option>Pending</option>
                        <option>Processing</option>
                        <option>Shipped</option>
                        <option>Delivered</option>
                        <option>Cancelled</option>
                    </select>
                    
                    <select class="enhanced-select">
                        <option>All Payment Methods</option>
                        <option>M-Pesa</option>
                        <option>Bank Transfer</option>
                        <option>Cash on Delivery</option>
                    </select>
                    
                    <select class="enhanced-select">
                        <option>Last 30 days</option>
                        <option>Last 7 days</option>
                        <option>Last 3 months</option>
                        <option>Custom Range</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="glassmorphism-card animate-fade-in-up" style="animation-delay: 0.4s;">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gradient-to-br from-harrier-red to-harrier-dark rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-shopping-cart text-white"></i>
                    </div>
                    <h3 class="text-lg font-bold text-harrier-dark font-montserrat">Recent Orders</h3>
                </div>
                <div class="flex gap-2">
                    <button onclick="refreshOrderData()" class="enhanced-btn enhanced-btn-secondary text-sm">
                        <i class="fas fa-sync-alt mr-1"></i>Refresh
                    </button>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gradient-to-r from-gray-50 to-gray-100">
                    <tr>
                        <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                            <input type="checkbox" class="rounded border-gray-300 text-harrier-red focus:ring-harrier-red">
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                            Order Details
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                            Customer
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                            Products
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                            Amount
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                            Status
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                            Payment
                        </th>
                        <th class="px-6 py-4 text-center text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr class="hover:bg-gray-50 transition-colors duration-200">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" class="rounded border-gray-300 text-harrier-red focus:ring-harrier-red">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-harrier-dark">#SP-2024-001</div>
                            <div class="text-sm text-gray-500">Dec 15, 2024 2:30 PM</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-user text-gray-600 text-sm"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">John Doe</div>
                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900">Brake Pads, Oil Filter</div>
                            <div class="text-sm text-gray-500">2 items • 3 units total</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-semibold text-green-600">KSh 4,500</div>
                            <div class="text-sm text-gray-500">Paid</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check-circle mr-1"></i>Delivered
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-2">
                                    <i class="fas fa-mobile-alt text-white text-xs"></i>
                                </div>
                                <span class="text-sm text-gray-900">M-Pesa</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <div class="flex justify-center gap-2">
                                <button onclick="viewOrderDetails('SP-2024-001')" class="pill-action-btn pill-btn-view">
                                    <i class="fas fa-eye mr-1"></i>View
                                </button>
                                <button onclick="updateOrderStatus('SP-2024-001')" class="pill-action-btn pill-btn-edit">
                                    <i class="fas fa-edit mr-1"></i>Update
                                </button>
                                <button onclick="printInvoice('SP-2024-001')" class="pill-action-btn pill-btn-restock">
                                    <i class="fas fa-print mr-1"></i>Print
                                </button>
                            </div>
                        </td>
                    </tr>
                    <!-- More rows would be dynamically generated -->
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="flex items-center justify-between px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div class="flex items-center text-sm text-gray-700">
                <span>Showing 1 to 10 of 332 results</span>
            </div>
            <div class="flex items-center space-x-2">
                <button class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                    Previous
                </button>
                <span class="px-3 py-2 text-sm font-medium text-white bg-harrier-red border border-harrier-red rounded-md">
                    1
                </span>
                <button class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                    2
                </button>
                <button class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                    3
                </button>
                <button class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                    Next
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
// Order Management JavaScript Functions
function exportOrderData() {
    showToast('Exporting order data...', 'info');
}

function bulkOrderActions() {
    showToast('Bulk actions panel - Implementation needed', 'info');
}

function createManualOrder() {
    showToast('Create manual order modal - Implementation needed', 'info');
}

function refreshOrderData() {
    showToast('Refreshing order data...', 'info');
}

function viewOrderDetails(orderId) {
    showToast(`Viewing details for order ${orderId}`, 'info');
}

function updateOrderStatus(orderId) {
    showToast(`Update status for order ${orderId}`, 'info');
}

function printInvoice(orderId) {
    showToast(`Printing invoice for order ${orderId}`, 'info');
}

// Toast notification function
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;
    
    switch(type) {
        case 'success':
            toast.className += ' bg-green-500 text-white';
            break;
        case 'error':
            toast.className += ' bg-red-500 text-white';
            break;
        case 'warning':
            toast.className += ' bg-orange-500 text-white';
            break;
        default:
            toast.className += ' bg-blue-500 text-white';
    }
    
    toast.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-info-circle mr-2"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);
    
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}
</script>
{% endblock %}
