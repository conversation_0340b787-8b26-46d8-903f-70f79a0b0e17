{% extends 'base_admin_dashboard.html' %}
{% load static %}

{% block title %}Message Management - Admin Dashboard{% endblock %}

{% block page_title %}Message Management{% endblock %}
{% block page_description %}Create and manage system messages, announcements, and notifications{% endblock %}

{% block extra_css %}
<style>
    /* Enhanced Message Management Styles with Harrier Design */
    .message-management-container {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .glassmorphism-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 
            0 8px 32px rgba(0, 0, 0, 0.1),
            0 2px 8px rgba(0, 0, 0, 0.05);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .glassmorphism-card:hover {
        transform: translateY(-2px);
        box-shadow: 
            0 12px 40px rgba(0, 0, 0, 0.15),
            0 4px 12px rgba(0, 0, 0, 0.1);
    }

    /* Stats Cards */
    .stat-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--harrier-red), var(--harrier-dark));
        border-radius: 16px 16px 0 0;
    }

    .stat-card:hover {
        transform: translateY(-4px) scale(1.02);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: 800;
        font-family: 'Montserrat', sans-serif;
        background: linear-gradient(135deg, var(--harrier-red), var(--harrier-dark));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        line-height: 1;
    }

    .stat-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: #64748b;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-top: 0.5rem;
        font-family: 'Raleway', sans-serif;
    }

    .stat-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
    }

    /* Tab Navigation */
    .tab-navigation {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(15px);
        border-radius: 16px;
        padding: 0.5rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .message-tab {
        display: inline-flex;
        align-items: center;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        font-size: 0.875rem;
        color: #64748b;
        background: transparent;
        border: none;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        font-family: 'Raleway', sans-serif;
        text-decoration: none;
        margin: 0 0.25rem;
    }

    .message-tab:hover {
        color: var(--harrier-red);
        background: rgba(220, 38, 38, 0.1);
        transform: translateY(-1px);
    }

    .message-tab.active {
        background: linear-gradient(135deg, var(--harrier-red), var(--harrier-dark));
        color: white;
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    }

    .message-tab i {
        margin-right: 0.5rem;
        font-size: 1rem;
    }

    /* Action Buttons */
    .admin-request-add-btn {
        background: linear-gradient(135deg, var(--harrier-red) 0%, var(--harrier-dark) 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        display: inline-flex;
        align-items: center;
        text-decoration: none;
        font-family: 'Montserrat', sans-serif;
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    }

    .admin-request-add-btn:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 8px 20px rgba(220, 38, 38, 0.4);
        color: white;
        text-decoration: none;
    }

    .admin-request-add-btn i {
        margin-right: 0.5rem;
        font-size: 1rem;
    }

    /* Search and Filter Section */
    .search-filter-section {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(15px);
        border-radius: 16px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .search-input {
        background: rgba(255, 255, 255, 0.8);
        border: 2px solid rgba(226, 232, 240, 0.8);
        border-radius: 12px;
        padding: 0.75rem 1rem 0.75rem 2.5rem;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        width: 100%;
        font-family: 'Raleway', sans-serif;
    }

    .search-input:focus {
        outline: none;
        border-color: var(--harrier-red);
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
        background: white;
    }

    .filter-select {
        background: rgba(255, 255, 255, 0.8);
        border: 2px solid rgba(226, 232, 240, 0.8);
        border-radius: 12px;
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        font-family: 'Raleway', sans-serif;
    }

    .filter-select:focus {
        outline: none;
        border-color: var(--harrier-red);
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
        background: white;
    }

    /* Animation Classes */
    .animate-fade-in-up {
        animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        opacity: 0;
        transform: translateY(20px);
    }

    @keyframes fadeInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Loading States */
    .htmx-request .search-input,
    .htmx-request .filter-select {
        opacity: 0.7;
        pointer-events: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .stat-card {
            padding: 1rem;
        }
        
        .stat-value {
            font-size: 2rem;
        }
        
        .message-tab {
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
        }
        
        .admin-request-add-btn {
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
        }
    }

    /* CSS Variables for Harrier Colors */
    :root {
        --harrier-red: #dc2626;
        --harrier-dark: #1f2937;
        --harrier-blue: #1e40af;
        --harrier-gray: #f8fafc;
    }
</style>
{% endblock %}

{% block dashboard_content %}
<div class="message-management-container">
    <!-- Enhanced Header Section -->
    <div class="animate-fade-in-up" style="animation-delay: 0.1s;">
        <div class="glassmorphism-card p-6 mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                <div>
                    <h1 class="text-3xl font-bold text-harrier-dark font-montserrat mb-2">
                        <i class="fas fa-envelope mr-3 text-harrier-red"></i>
                        Message Management
                    </h1>
                    <p class="text-gray-600 font-raleway">
                        Create, manage, and track system messages, announcements, and user notifications
                    </p>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="admin-request-add-btn"
                            hx-get="{% url 'core:admin_message_create_modal' %}"
                            hx-target="body"
                            hx-swap="beforeend">
                        <i class="fas fa-plus"></i>
                        Create Message
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 animate-fade-in-up" style="animation-delay: 0.2s;">
        <!-- Total Messages -->
        <div class="stat-card">
            <div class="stat-icon bg-gradient-to-r from-blue-500 to-blue-600">
                <i class="fas fa-envelope"></i>
            </div>
            <div class="stat-value">{{ total_messages|default:0 }}</div>
            <div class="stat-label">Total Messages</div>
        </div>

        <!-- Active Messages -->
        <div class="stat-card">
            <div class="stat-icon bg-gradient-to-r from-green-500 to-green-600">
                <i class="fas fa-broadcast-tower"></i>
            </div>
            <div class="stat-value">{{ active_messages|default:0 }}</div>
            <div class="stat-label">Active Messages</div>
        </div>

        <!-- Scheduled Messages -->
        <div class="stat-card">
            <div class="stat-icon bg-gradient-to-r from-yellow-500 to-yellow-600">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-value">{{ scheduled_messages|default:0 }}</div>
            <div class="stat-label">Scheduled</div>
        </div>

        <!-- Draft Messages -->
        <div class="stat-card">
            <div class="stat-icon bg-gradient-to-r from-gray-500 to-gray-600">
                <i class="fas fa-edit"></i>
            </div>
            <div class="stat-value">{{ draft_messages|default:0 }}</div>
            <div class="stat-label">Drafts</div>
        </div>
    </div>

    <!-- Enhanced Tab Navigation -->
    <div class="animate-fade-in-up" style="animation-delay: 0.3s;">
        <div class="tab-navigation">
            <button class="message-tab active" data-tab="messages"
                    hx-get="{% url 'core:admin_message_list_tab' %}"
                    hx-target="#message-tab-content"
                    hx-swap="innerHTML">
                <i class="fas fa-list"></i>
                <span>All Messages</span>
            </button>
            <button class="message-tab" data-tab="analytics"
                    hx-get="{% url 'core:admin_message_analytics_tab' %}"
                    hx-target="#message-tab-content"
                    hx-swap="innerHTML">
                <i class="fas fa-chart-line"></i>
                <span>Analytics</span>
            </button>
        </div>
    </div>

    <!-- Dynamic Tab Content -->
    <div id="message-tab-content" class="animate-fade-in-up" style="animation-delay: 0.4s;">
        <!-- Content will be loaded via HTMX -->
        <div class="glassmorphism-card p-6">
            <div class="text-center py-8">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-harrier-red mx-auto mb-4"></div>
                <p class="text-gray-600 font-raleway">Loading messages...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize message management features
    initializeMessageManagement();
    
    // Load initial tab content
    setTimeout(() => {
        const activeTab = document.querySelector('.message-tab.active');
        if (activeTab) {
            htmx.trigger(activeTab, 'click');
        }
    }, 500);
});

function initializeMessageManagement() {
    // Tab switching functionality
    document.addEventListener('click', function(event) {
        if (event.target.closest('.message-tab')) {
            const clickedTab = event.target.closest('.message-tab');
            
            // Update active state
            document.querySelectorAll('.message-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            clickedTab.classList.add('active');
        }
    });
    
    // Enhanced HTMX event handling
    document.addEventListener('htmx:afterRequest', function(event) {
        if (event.target.classList.contains('message-tab')) {
            // Re-initialize any JavaScript functionality after tab load
            initializeTabContent();
        }
    });
    
    // Handle HTMX errors gracefully
    document.addEventListener('htmx:responseError', function(event) {
        showToast('Failed to load content. Please try again.', 'error');
    });
}

function initializeTabContent() {
    // Re-initialize any components that need setup after HTMX loads content
    console.log('Tab content loaded and initialized');
}

// Enhanced error handling
document.addEventListener('htmx:responseError', function(event) {
    showToast('Failed to load content. Please try again.', 'error');
});

// Success handling for HTMX requests
document.addEventListener('htmx:afterRequest', function(event) {
    if (event.detail.successful && event.target.classList.contains('message-tab')) {
        // Show subtle success feedback for tab loads
        const tabName = event.target.textContent.trim();
        console.log(`${tabName} loaded successfully`);
    }
});
</script>
{% endblock %}
