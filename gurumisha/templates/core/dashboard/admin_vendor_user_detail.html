{% extends 'base_admin_dashboard.html' %}
{% load static %}

{% block admin_title %}Vendor Profile - {{ vendor.company_name }}{% endblock %}
{% block page_title %}Vendor Management{% endblock %}
{% block page_description %}Comprehensive vendor profile management and business analytics{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/profile-forms.css' %}">
<link rel="stylesheet" href="{% static 'css/enhanced-inputs.css' %}">
<link rel="stylesheet" href="{% static 'css/global-animations.css' %}">
<style>
/* Enhanced Vendor Detail Page Styles */
.vendor-detail-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
}

.vendor-hero-section {
    background: linear-gradient(135deg, #DC2626 0%, #1e293b 50%, #1e40af 100%);
    position: relative;
    overflow: hidden;
}

.vendor-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.3;
}

.vendor-avatar-container {
    position: relative;
    display: inline-block;
}

.vendor-avatar {
    width: 160px;
    height: 160px;
    border-radius: 2rem;
    border: 4px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.vendor-avatar:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

.company-logo {
    width: 120px;
    height: 120px;
    border-radius: 1.5rem;
    border: 3px solid rgba(255, 255, 255, 0.3);
    object-fit: cover;
    background: rgba(255, 255, 255, 0.1);
}

.verification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid white;
    font-size: 16px;
    font-weight: bold;
}

.verified {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.pending {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.suspended {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.business-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.business-stat-card {
    background: white;
    border-radius: 1.5rem;
    padding: 2rem;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.business-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #DC2626, #1e40af);
}

.business-stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    color: white;
}

.tab-navigation {
    display: flex;
    background: white;
    border-radius: 1rem;
    padding: 0.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.tab-button {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    background: transparent;
    border-radius: 0.5rem;
    font-weight: 600;
    color: #6b7280;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-width: 120px;
}

.tab-button.active {
    background: linear-gradient(135deg, #DC2626, #1e40af);
    color: white;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.tab-button:hover:not(.active) {
    background: #f3f4f6;
    color: #374151;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.content-section {
    background: white;
    border-radius: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.section-header {
    padding: 2rem 2rem 0 2rem;
    border-bottom: 1px solid #f3f4f6;
    margin-bottom: 0;
}

.section-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.25rem;
    color: white;
}

.action-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-decoration: none;
    min-width: 120px;
}

.action-button.primary {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.action-button.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.action-button.danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.action-button.danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

.action-button.secondary {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

.action-button.secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(107, 114, 128, 0.4);
}

.glassmorphism {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .vendor-avatar {
        width: 120px;
        height: 120px;
    }
    
    .company-logo {
        width: 80px;
        height: 80px;
    }
    
    .business-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .tab-navigation {
        flex-direction: column;
    }
    
    .tab-button {
        flex: none;
        width: 100%;
    }
}
</style>
{% endblock %}

{% block dashboard_content %}
<div class="vendor-detail-container">
    <!-- Enhanced Vendor Hero Section -->
    <div class="vendor-hero-section rounded-2xl p-8 mb-8 animate-fade-in-up">
        <div class="relative z-10">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-8">
                <!-- Vendor Information -->
                <div class="flex flex-col md:flex-row md:items-center gap-6">
                    <!-- Profile Picture & Company Logo Section -->
                    <div class="flex items-center gap-6">
                        <!-- User Profile Picture -->
                        <div class="vendor-avatar-container">
                            {% if user_profile.profile_picture %}
                                <img src="{{ user_profile.profile_picture.url }}"
                                     alt="{{ user_profile.get_full_name }}"
                                     class="vendor-avatar object-cover">
                            {% else %}
                                <div class="vendor-avatar bg-gradient-to-br from-white/20 to-white/10 flex items-center justify-center text-white text-4xl font-bold font-montserrat">
                                    {{ user_profile.first_name|first|default:user_profile.username|first|upper }}{{ user_profile.last_name|first|upper }}
                                </div>
                            {% endif %}
                            
                            <!-- Verification Badge -->
                            <div class="verification-badge {% if vendor.is_approved %}verified{% elif user_profile.is_active %}pending{% else %}suspended{% endif %}">
                                <i class="fas fa-{% if vendor.is_approved %}shield-check{% elif user_profile.is_active %}clock{% else %}ban{% endif %}"></i>
                            </div>
                        </div>

                        <!-- Company Logo -->
                        {% if vendor.company_logo %}
                        <div class="company-logo-container">
                            <img src="{{ vendor.company_logo.url }}"
                                 alt="{{ vendor.company_name }}"
                                 class="company-logo">
                        </div>
                        {% endif %}
                    </div>

                    <!-- Basic Information Section -->
                    <div class="text-white space-y-4">
                        <!-- Company Name and User Info -->
                        <div class="space-y-3">
                            <div class="flex flex-wrap items-center gap-3">
                                <h1 class="text-4xl font-bold font-montserrat bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
                                    {{ vendor.company_name }}
                                </h1>
                            </div>
                            <div class="flex flex-wrap items-center gap-3">
                                <p class="text-blue-100 text-lg font-raleway">
                                    <i class="fas fa-user mr-2"></i>{{ user_profile.get_full_name|default:user_profile.username }}
                                </p>
                                <span class="inline-flex items-center px-4 py-2 rounded-2xl text-sm font-semibold glassmorphism text-orange-200">
                                    <i class="fas fa-store mr-2"></i>{{ vendor.get_business_type_display }}
                                </span>
                                {% if user_profile.is_email_verified %}
                                <span class="inline-flex items-center px-3 py-1 rounded-xl text-xs font-medium glassmorphism text-green-200">
                                    <i class="fas fa-shield-check mr-1"></i>Email Verified
                                </span>
                                {% endif %}
                            </div>
                            <p class="text-blue-100 text-sm">@{{ user_profile.username }} • Member since {{ user_profile.date_joined|date:"M Y" }}</p>
                        </div>

                        <!-- Business Status Indicators -->
                        <div class="flex flex-wrap items-center gap-3">
                            {% if vendor.is_approved %}
                                <span class="inline-flex items-center px-4 py-2 glassmorphism rounded-full text-green-200 text-sm font-medium">
                                    <i class="fas fa-shield-check mr-2"></i>Verified Business
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-4 py-2 glassmorphism rounded-full text-yellow-200 text-sm font-medium">
                                    <i class="fas fa-clock mr-2"></i>Pending Verification
                                </span>
                            {% endif %}
                            
                            {% if subscription %}
                                <span class="inline-flex items-center px-4 py-2 glassmorphism rounded-full text-purple-200 text-sm font-medium">
                                    <i class="fas fa-crown mr-2"></i>{{ subscription.get_tier_display }} Plan
                                </span>
                            {% endif %}
                            
                            <span class="inline-flex items-center px-4 py-2 glassmorphism rounded-full text-blue-200 text-sm font-medium">
                                <i class="fas fa-{% if user_profile.is_active %}check-circle{% else %}times-circle{% endif %} mr-2"></i>
                                {% if user_profile.is_active %}Active{% else %}Inactive{% endif %}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col gap-4">
                    <!-- Primary Actions -->
                    <div class="flex flex-col sm:flex-row gap-3">
                        {% if not vendor.is_approved %}
                        <form method="post" class="inline-block">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="approve_vendor">
                            <button type="submit" class="action-button primary w-full sm:w-auto">
                                <i class="fas fa-check-circle mr-2"></i>Approve Vendor
                            </button>
                        </form>
                        {% else %}
                        <form method="post" class="inline-block">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="suspend_vendor">
                            <button type="submit" class="action-button danger w-full sm:w-auto">
                                <i class="fas fa-ban mr-2"></i>Suspend Vendor
                            </button>
                        </form>
                        {% endif %}

                        <form method="post" class="inline-block">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="toggle_status">
                            <button type="submit" class="action-button secondary w-full sm:w-auto">
                                <i class="fas fa-{% if user_profile.is_active %}user-slash{% else %}user-check{% endif %} mr-2"></i>
                                {% if user_profile.is_active %}Deactivate{% else %}Activate{% endif %}
                            </button>
                        </form>
                    </div>

                    <!-- Secondary Actions -->
                    <div class="flex flex-col sm:flex-row gap-3">
                        <button type="button" class="action-button secondary w-full sm:w-auto" onclick="openPasswordResetModal()">
                            <i class="fas fa-key mr-2"></i>Reset Password
                        </button>

                        <a href="{% url 'core:admin_listings' %}?vendor={{ vendor.id }}" class="action-button secondary w-full sm:w-auto text-center">
                            <i class="fas fa-car mr-2"></i>View Listings
                        </a>
                    </div>

                    <a href="{% url 'core:admin_vendors' %}" class="action-button secondary w-full text-center">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Vendors
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Business Statistics Grid -->
    <div class="business-stats-grid animate-fade-in-up" style="animation-delay: 0.2s;">
        <div class="business-stat-card">
            <div class="stat-icon bg-gradient-to-br from-blue-500 to-blue-600">
                <i class="fas fa-eye"></i>
            </div>
            <div class="text-3xl font-bold text-harrier-dark font-montserrat mb-2">
                {{ analytics_data.analytics.total_profile_views|default:0 }}
            </div>
            <div class="text-sm font-medium text-gray-600 mb-1">Total Profile Views</div>
            <div class="text-xs text-blue-600">{{ analytics_data.analytics.profile_views_this_month|default:0 }} this month</div>
        </div>

        <div class="business-stat-card">
            <div class="stat-icon bg-gradient-to-br from-green-500 to-green-600">
                <i class="fas fa-envelope"></i>
            </div>
            <div class="text-3xl font-bold text-harrier-dark font-montserrat mb-2">
                {{ analytics_data.analytics.total_inquiries|default:0 }}
            </div>
            <div class="text-sm font-medium text-gray-600 mb-1">Total Inquiries</div>
            <div class="text-xs text-green-600">{{ analytics_data.analytics.inquiries_this_month|default:0 }} this month</div>
        </div>

        <div class="business-stat-card">
            <div class="stat-icon bg-gradient-to-br from-purple-500 to-purple-600">
                <i class="fas fa-car"></i>
            </div>
            <div class="text-3xl font-bold text-harrier-dark font-montserrat mb-2">
                {{ vendor_listings.count|default:0 }}
            </div>
            <div class="text-sm font-medium text-gray-600 mb-1">Active Listings</div>
            <div class="text-xs text-purple-600">{{ vendor.get_subscription_tier|title }} tier</div>
        </div>

        <div class="business-stat-card">
            <div class="stat-icon bg-gradient-to-br from-orange-500 to-orange-600">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="text-3xl font-bold text-harrier-dark font-montserrat mb-2">
                {{ analytics_data.analytics.overall_performance_score|default:0 }}%
            </div>
            <div class="text-sm font-medium text-gray-600 mb-1">Performance Score</div>
            <div class="text-xs text-orange-600">{{ analytics_data.analytics.inquiry_response_rate|default:0 }}% response rate</div>
        </div>
    </div>

    <!-- Tab Navigation -->
    <div class="tab-navigation animate-fade-in-up" style="animation-delay: 0.4s;">
        <button class="tab-button active" onclick="showTab('overview')">
            <i class="fas fa-chart-pie mr-2"></i>Overview
        </button>
        <button class="tab-button" onclick="showTab('business')">
            <i class="fas fa-building mr-2"></i>Business Info
        </button>
        <button class="tab-button" onclick="showTab('listings')">
            <i class="fas fa-car mr-2"></i>Listings
        </button>
        <button class="tab-button" onclick="showTab('analytics')">
            <i class="fas fa-chart-bar mr-2"></i>Analytics
        </button>
        <button class="tab-button" onclick="showTab('activity')">
            <i class="fas fa-history mr-2"></i>Activity
        </button>
        <button class="tab-button" onclick="showTab('settings')">
            <i class="fas fa-cog mr-2"></i>Settings
        </button>
    </div>

    <!-- Tab Content -->
    <div class="animate-fade-in-up" style="animation-delay: 0.6s;">
        <!-- Overview Tab -->
        <div class="tab-content active" id="overview-tab">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Business Summary -->
                <div class="content-section">
                    <div class="section-header">
                        <div class="flex items-center">
                            <div class="section-icon bg-gradient-to-br from-blue-500 to-blue-600">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-harrier-dark font-montserrat">Business Summary</h3>
                                <p class="text-sm text-gray-600 font-raleway">Key business information and status</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex justify-between items-center py-2 border-b border-gray-100">
                                <span class="text-sm font-medium text-gray-600">Company Name</span>
                                <span class="text-sm font-bold text-harrier-dark">{{ vendor.company_name }}</span>
                            </div>
                            <div class="flex justify-between items-center py-2 border-b border-gray-100">
                                <span class="text-sm font-medium text-gray-600">Business Type</span>
                                <span class="text-sm font-bold text-harrier-dark">{{ vendor.get_business_type_display }}</span>
                            </div>
                            <div class="flex justify-between items-center py-2 border-b border-gray-100">
                                <span class="text-sm font-medium text-gray-600">Verification Status</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {% if vendor.is_approved %}bg-green-100 text-green-800{% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                    <i class="fas fa-{% if vendor.is_approved %}shield-check{% else %}clock{% endif %} mr-1"></i>
                                    {% if vendor.is_approved %}Verified{% else %}Pending{% endif %}
                                </span>
                            </div>
                            <div class="flex justify-between items-center py-2 border-b border-gray-100">
                                <span class="text-sm font-medium text-gray-600">Account Status</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {% if user_profile.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                    <i class="fas fa-{% if user_profile.is_active %}check-circle{% else %}times-circle{% endif %} mr-1"></i>
                                    {% if user_profile.is_active %}Active{% else %}Inactive{% endif %}
                                </span>
                            </div>
                            <div class="flex justify-between items-center py-2 border-b border-gray-100">
                                <span class="text-sm font-medium text-gray-600">Member Since</span>
                                <span class="text-sm font-bold text-harrier-dark">{{ vendor.created_at|date:"M d, Y" }}</span>
                            </div>
                            {% if vendor.approval_date %}
                            <div class="flex justify-between items-center py-2">
                                <span class="text-sm font-medium text-gray-600">Approved Date</span>
                                <span class="text-sm font-bold text-harrier-dark">{{ vendor.approval_date|date:"M d, Y" }}</span>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Recent Activity Summary -->
                <div class="content-section">
                    <div class="section-header">
                        <div class="flex items-center">
                            <div class="section-icon bg-gradient-to-br from-green-500 to-green-600">
                                <i class="fas fa-activity"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-harrier-dark font-montserrat">Recent Activity</h3>
                                <p class="text-sm text-gray-600 font-raleway">Latest vendor activities and interactions</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            {% for activity in recent_activities|slice:":5" %}
                            <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                                <div class="w-8 h-8 bg-gradient-to-br from-harrier-red to-harrier-red-dark rounded-full flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-circle text-white text-xs"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="text-sm font-medium text-harrier-dark">{{ activity.get_action_display }}</div>
                                    {% if activity.description %}
                                        <div class="text-xs text-gray-600 mt-1">{{ activity.description }}</div>
                                    {% endif %}
                                    <div class="text-xs text-gray-500 mt-1">{{ activity.timestamp|timesince }} ago</div>
                                </div>
                            </div>
                            {% empty %}
                            <div class="text-center py-8">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-history text-gray-400 text-xl"></i>
                                </div>
                                <p class="text-gray-500 font-raleway">No recent activity</p>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Info Tab -->
        <div class="tab-content" id="business-tab">
            <form method="post" enctype="multipart/form-data" class="space-y-6">
                {% csrf_token %}
                <input type="hidden" name="action" value="update_profile">

                <!-- Company Information -->
                <div class="content-section">
                    <div class="section-header">
                        <div class="flex items-center">
                            <div class="section-icon bg-gradient-to-br from-orange-500 to-orange-600">
                                <i class="fas fa-building"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-harrier-dark font-montserrat">Company Information</h3>
                                <p class="text-sm text-gray-600 font-raleway">Business details and corporate identity</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="{{ vendor_form.company_name.id_for_label }}" class="form-label">
                                    <i class="fas fa-building mr-2 text-orange-500"></i>Company Name
                                </label>
                                {{ vendor_form.company_name }}
                            </div>

                            <div class="form-group">
                                <label for="{{ vendor_form.business_license.id_for_label }}" class="form-label">
                                    <i class="fas fa-certificate mr-2 text-blue-500"></i>Business License
                                </label>
                                {{ vendor_form.business_license }}
                            </div>

                            <div class="form-group">
                                <label for="{{ vendor_form.business_type.id_for_label }}" class="form-label">
                                    <i class="fas fa-industry mr-2 text-purple-500"></i>Business Type
                                </label>
                                {{ vendor_form.business_type }}
                            </div>

                            <div class="form-group">
                                <label for="{{ vendor_form.year_established.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar mr-2 text-green-500"></i>Year Established
                                </label>
                                {{ vendor_form.year_established }}
                            </div>
                        </div>

                        <div class="form-group mt-6">
                            <label for="{{ vendor_form.description.id_for_label }}" class="form-label">
                                <i class="fas fa-file-alt mr-2 text-indigo-500"></i>Business Description
                            </label>
                            {{ vendor_form.description }}
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="content-section">
                    <div class="section-header">
                        <div class="flex items-center">
                            <div class="section-icon bg-gradient-to-br from-blue-500 to-blue-600">
                                <i class="fas fa-address-book"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-harrier-dark font-montserrat">Contact Information</h3>
                                <p class="text-sm text-gray-600 font-raleway">Business contact details and communication</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="{{ vendor_form.business_phone.id_for_label }}" class="form-label">
                                    <i class="fas fa-phone mr-2 text-green-500"></i>Business Phone
                                </label>
                                {{ vendor_form.business_phone }}
                            </div>

                            <div class="form-group">
                                <label for="{{ vendor_form.business_email.id_for_label }}" class="form-label">
                                    <i class="fas fa-envelope mr-2 text-blue-500"></i>Business Email
                                </label>
                                {{ vendor_form.business_email }}
                            </div>

                            <div class="form-group md:col-span-2">
                                <label for="{{ vendor_form.website.id_for_label }}" class="form-label">
                                    <i class="fas fa-globe mr-2 text-purple-500"></i>Website
                                </label>
                                {{ vendor_form.website }}
                            </div>
                        </div>

                        <div class="form-group mt-6">
                            <label for="{{ vendor_form.physical_address.id_for_label }}" class="form-label">
                                <i class="fas fa-map-marker-alt mr-2 text-red-500"></i>Physical Address
                            </label>
                            {{ vendor_form.physical_address }}
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end gap-4">
                    <button type="button" class="action-button secondary" onclick="resetForm()">
                        <i class="fas fa-undo mr-2"></i>Reset Changes
                    </button>
                    <button type="submit" class="action-button primary">
                        <i class="fas fa-save mr-2"></i>Update Business Profile
                    </button>
                </div>
            </form>
        </div>

        <!-- Listings Tab -->
        <div class="tab-content" id="listings-tab">
            <div class="content-section">
                <div class="section-header">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="section-icon bg-gradient-to-br from-purple-500 to-purple-600">
                                <i class="fas fa-car"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-harrier-dark font-montserrat">Vehicle Listings</h3>
                                <p class="text-sm text-gray-600 font-raleway">Vendor's active and inactive car listings</p>
                            </div>
                        </div>
                        <a href="{% url 'core:admin_listings' %}?vendor={{ vendor.id }}" class="action-button primary">
                            <i class="fas fa-external-link-alt mr-2"></i>View All
                        </a>
                    </div>
                </div>
                <div class="p-6">
                    {% if vendor_listings %}
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {% for listing in vendor_listings|slice:":6" %}
                        <div class="bg-white border border-gray-200 rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300">
                            {% if listing.images.first %}
                                <img src="{{ listing.images.first.image.url }}" alt="{{ listing.title }}" class="w-full h-48 object-cover">
                            {% else %}
                                <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                    <i class="fas fa-car text-gray-400 text-3xl"></i>
                                </div>
                            {% endif %}
                            <div class="p-4">
                                <h4 class="font-bold text-harrier-dark font-montserrat mb-2">{{ listing.title }}</h4>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-lg font-bold text-harrier-red">${{ listing.price|floatformat:0 }}</span>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        {% if listing.is_approved %}bg-green-100 text-green-800{% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                        {% if listing.is_approved %}Approved{% else %}Pending{% endif %}
                                    </span>
                                </div>
                                <div class="text-sm text-gray-600">
                                    <div>{{ listing.year }} {{ listing.brand.name }} {{ listing.model.name }}</div>
                                    <div>{{ listing.mileage|floatformat:0 }} km • {{ listing.fuel_type }}</div>
                                </div>
                                <div class="mt-3 flex gap-2">
                                    <a href="{% url 'core:admin_car_detail' listing.id %}" class="flex-1 text-center py-2 px-3 bg-blue-100 text-blue-700 rounded-lg text-sm font-medium hover:bg-blue-200 transition-colors">
                                        View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-12">
                        <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-car text-gray-400 text-2xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-600 font-montserrat mb-2">No Listings Yet</h4>
                        <p class="text-gray-500 font-raleway">This vendor hasn't created any car listings yet.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Analytics Tab -->
        <div class="tab-content" id="analytics-tab">
            <div class="content-section">
                <div class="section-header">
                    <div class="flex items-center">
                        <div class="section-icon bg-gradient-to-br from-indigo-500 to-indigo-600">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-harrier-dark font-montserrat">Performance Analytics</h3>
                            <p class="text-sm text-gray-600 font-raleway">Detailed vendor performance metrics and insights</p>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    {% if analytics_data.analytics %}
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 text-center border border-blue-200">
                            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-eye text-white text-xl"></i>
                            </div>
                            <div class="text-3xl font-bold text-blue-600 font-montserrat mb-1">{{ analytics_data.analytics.total_profile_views }}</div>
                            <div class="text-sm font-medium text-blue-600">Total Profile Views</div>
                            <div class="text-xs text-blue-500 mt-1">{{ analytics_data.analytics.profile_views_this_month }} this month</div>
                        </div>

                        <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 text-center border border-green-200">
                            <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-envelope text-white text-xl"></i>
                            </div>
                            <div class="text-3xl font-bold text-green-600 font-montserrat mb-1">{{ analytics_data.analytics.total_inquiries }}</div>
                            <div class="text-sm font-medium text-green-600">Total Inquiries</div>
                            <div class="text-xs text-green-500 mt-1">{{ analytics_data.analytics.inquiries_this_month }} this month</div>
                        </div>

                        <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 text-center border border-purple-200">
                            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-chart-line text-white text-xl"></i>
                            </div>
                            <div class="text-3xl font-bold text-purple-600 font-montserrat mb-1">{{ analytics_data.analytics.overall_performance_score }}</div>
                            <div class="text-sm font-medium text-purple-600">Performance Score</div>
                            <div class="text-xs text-purple-500 mt-1">{{ analytics_data.analytics.profile_completion_percentage }}% complete</div>
                        </div>

                        <div class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-6 text-center border border-orange-200">
                            <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-clock text-white text-xl"></i>
                            </div>
                            <div class="text-3xl font-bold text-orange-600 font-montserrat mb-1">{{ analytics_data.analytics.average_response_time_hours }}</div>
                            <div class="text-sm font-medium text-orange-600">Avg Response Time</div>
                            <div class="text-xs text-orange-500 mt-1">{{ analytics_data.analytics.inquiry_response_rate }}% response rate</div>
                        </div>
                    </div>

                    <!-- Performance Charts -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="bg-white rounded-2xl p-6 border border-gray-200">
                            <h4 class="text-lg font-bold text-harrier-dark font-montserrat mb-4">Business Performance</h4>
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Profile Completion</span>
                                    <div class="flex items-center gap-2">
                                        <div class="w-24 bg-gray-200 rounded-full h-2">
                                            <div class="bg-blue-500 h-2 rounded-full" style="width: {{ analytics_data.analytics.profile_completion_percentage }}%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-700">{{ analytics_data.analytics.profile_completion_percentage }}%</span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Response Rate</span>
                                    <div class="flex items-center gap-2">
                                        <div class="w-24 bg-gray-200 rounded-full h-2">
                                            <div class="bg-green-500 h-2 rounded-full" style="width: {{ analytics_data.analytics.inquiry_response_rate }}%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-700">{{ analytics_data.analytics.inquiry_response_rate }}%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-2xl p-6 border border-gray-200">
                            <h4 class="text-lg font-bold text-harrier-dark font-montserrat mb-4">Monthly Trends</h4>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Profile Views</span>
                                    <span class="text-sm font-bold text-harrier-dark">{{ analytics_data.analytics.profile_views_this_month }}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">New Inquiries</span>
                                    <span class="text-sm font-bold text-harrier-dark">{{ analytics_data.analytics.inquiries_this_month }}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Active Listings</span>
                                    <span class="text-sm font-bold text-harrier-dark">{{ vendor_listings.count }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="text-center py-12">
                        <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-chart-line text-gray-400 text-2xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-600 font-montserrat mb-2">No Analytics Data</h4>
                        <p class="text-gray-500 font-raleway">Analytics data is not available for this vendor.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Activity Tab -->
        <div class="tab-content" id="activity-tab">
            <div class="content-section">
                <div class="section-header">
                    <div class="flex items-center">
                        <div class="section-icon bg-gradient-to-br from-red-500 to-red-600">
                            <i class="fas fa-history"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-harrier-dark font-montserrat">Activity Log</h3>
                            <p class="text-sm text-gray-600 font-raleway">Detailed vendor activity history and system interactions</p>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    {% if recent_activities %}
                    <div class="space-y-4">
                        {% for activity in recent_activities %}
                        <div class="flex items-start space-x-4 p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl border border-gray-200 hover:shadow-md transition-all duration-300">
                            <div class="w-12 h-12 bg-gradient-to-br from-harrier-red to-harrier-red-dark rounded-full flex items-center justify-center shadow-lg flex-shrink-0">
                                <i class="fas fa-circle text-white text-xs"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="font-semibold text-harrier-dark font-montserrat">{{ activity.get_action_display }}</div>
                                {% if activity.description %}
                                    <div class="text-sm text-gray-600 mt-1 font-raleway">{{ activity.description }}</div>
                                {% endif %}
                                <div class="flex items-center justify-between mt-2">
                                    <div class="text-xs text-gray-500 font-medium">{{ activity.timestamp|timesince }} ago</div>
                                    {% if activity.ip_address %}
                                        <div class="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded-full">{{ activity.ip_address }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-12">
                        <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-history text-gray-400 text-2xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-600 font-montserrat mb-2">No Activity Yet</h4>
                        <p class="text-gray-500 font-raleway">No activity records found for this vendor.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div class="tab-content" id="settings-tab">
            <div class="content-section">
                <div class="section-header">
                    <div class="flex items-center">
                        <div class="section-icon bg-gradient-to-br from-gray-500 to-gray-600">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-harrier-dark font-montserrat">Account Settings</h3>
                            <p class="text-sm text-gray-600 font-raleway">Vendor account management and administrative controls</p>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Account Actions -->
                        <div class="space-y-4">
                            <h4 class="font-bold text-harrier-dark font-montserrat mb-4">Account Actions</h4>

                            <form method="post" class="w-full">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="toggle_status">
                                <button type="submit" class="action-button w-full {% if user_profile.is_active %}danger{% else %}primary{% endif %}">
                                    <i class="fas fa-{% if user_profile.is_active %}user-slash{% else %}user-check{% endif %} mr-2"></i>
                                    {% if user_profile.is_active %}Deactivate Account{% else %}Activate Account{% endif %}
                                </button>
                            </form>

                            <form method="post" class="w-full">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="reset_password">
                                <button type="submit" class="action-button secondary w-full">
                                    <i class="fas fa-key mr-2"></i>Reset Password
                                </button>
                            </form>

                            {% if vendor.is_approved %}
                            <form method="post" class="w-full">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="suspend_vendor">
                                <button type="submit" class="action-button danger w-full">
                                    <i class="fas fa-ban mr-2"></i>Suspend Vendor
                                </button>
                            </form>
                            {% else %}
                            <form method="post" class="w-full">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="approve_vendor">
                                <button type="submit" class="action-button primary w-full">
                                    <i class="fas fa-shield-check mr-2"></i>Approve Vendor
                                </button>
                            </form>
                            {% endif %}
                        </div>

                        <!-- Account Information -->
                        <div class="space-y-4">
                            <h4 class="font-bold text-harrier-dark font-montserrat mb-4">Account Information</h4>
                            <div class="bg-gray-50 rounded-lg p-4 space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-600">User ID</span>
                                    <span class="text-sm font-bold text-harrier-dark">#{{ user_profile.id }}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-600">Vendor ID</span>
                                    <span class="text-sm font-bold text-harrier-dark">#{{ vendor.id }}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-600">Email</span>
                                    <span class="text-sm font-bold text-harrier-dark">{{ user_profile.email }}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-600">Username</span>
                                    <span class="text-sm font-bold text-harrier-dark">{{ user_profile.username }}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-600">Last Login</span>
                                    <span class="text-sm font-bold text-harrier-dark">{{ user_profile.last_login|date:"M d, Y H:i"|default:"Never" }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Tab functionality
function showTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.remove('active');
    });

    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('active');
    });

    // Show selected tab content
    const selectedTab = document.getElementById(tabName + '-tab');
    if (selectedTab) {
        selectedTab.classList.add('active');
    }

    // Add active class to clicked button
    event.target.classList.add('active');
}

// Password reset modal (placeholder)
function openPasswordResetModal() {
    if (confirm('Are you sure you want to reset this vendor\'s password? A new password will be generated and displayed.')) {
        // Submit the password reset form
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            {% csrf_token %}
            <input type="hidden" name="action" value="reset_password">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Form reset functionality
function resetForm() {
    if (confirm('Are you sure you want to reset all changes?')) {
        location.reload();
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });

    // Add loading states to forms
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function() {
            const submitButton = form.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
            }
        });
    });
});
</script>
{% endblock %}
