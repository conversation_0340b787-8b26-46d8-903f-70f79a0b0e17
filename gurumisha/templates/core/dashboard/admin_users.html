{% extends 'base_admin_dashboard.html' %}
{% load static %}

{% block dashboard_title %}User Management{% endblock %}
{% block page_title %}User Management{% endblock %}
{% block page_description %}Comprehensive user management with advanced filtering and analytics{% endblock %}

{% block extra_css %}
{{ block.super }}
<link rel="stylesheet" href="{% static 'css/admin-users.css' %}">
<style>
    /* Additional page-specific styles */
    .page-header {
        background: linear-gradient(135deg, #1f2937 0%, #dc2626 100%);
        color: white;
        padding: 2rem;
        border-radius: 1rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-10px) rotate(5deg); }
    }

    /* Loading indicator */
    #loading-indicator {
        display: none;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 9999;
        background: rgba(255, 255, 255, 0.95);
        padding: 2rem;
        border-radius: 1rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);
    }

    .htmx-indicator {
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .htmx-request .htmx-indicator {
        opacity: 1;
    }

    .htmx-request.htmx-indicator {
        opacity: 1;
    }
</style>
{% endblock %}

{% comment %} {% block sidebar_nav %}
    <li>
        <a href="{% url 'core:dashboard' %}" class="dashboard-nav-link">
            <i class="fas fa-tachometer-alt"></i>
            <span>Dashboard</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_users' %}" class="dashboard-nav-link active">
            <i class="fas fa-users"></i>
            <span>User Management</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_vendors' %}" class="dashboard-nav-link">
            <i class="fas fa-store"></i>
            <span>Vendor Management</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_listings' %}" class="dashboard-nav-link">
            <i class="fas fa-car"></i>
            <span>Car Listings</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-ship"></i>
            <span>Import Requests</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-tools"></i>
            <span>Spare Parts</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-envelope"></i>
            <span>Inquiries</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-newspaper"></i>
            <span>Content Management</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_analytics' %}" class="dashboard-nav-link">
            <i class="fas fa-chart-bar"></i>
            <span>Analytics</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-cog"></i>
            <span>System Settings</span>
        </a>
    </li>
{% endblock %} {% endcomment %}

{% block breadcrumb_items %}
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">User Management</span>
        </div>
    </li>
{% endblock %}

{% block dashboard_content %}
    <!-- Loading Indicator -->
    <div id="loading-indicator" class="htmx-indicator">
        <div class="loading-spinner"></div>
        <p class="mt-4 text-sm font-medium text-gray-600 font-raleway">Loading...</p>
    </div>

    <!-- Enhanced User Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Users Card -->
        <div class="enhanced-stat-card">
            <div class="stat-icon bg-gradient-to-br from-blue-500 to-blue-600 text-white">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-value">{{ total_users|default:0 }}</div>
            <div class="stat-label">Total Users</div>
            <div class="stat-change positive">
                <i class="fas fa-arrow-up mr-1"></i>
                +{{ recent_registrations|default:0 }} this month
            </div>
        </div>

        <!-- Active Users Card -->
        <div class="enhanced-stat-card">
            <div class="stat-icon bg-gradient-to-br from-green-500 to-green-600 text-white">
                <i class="fas fa-user-check"></i>
            </div>
            <div class="stat-value">{{ active_users|default:0 }}</div>
            <div class="stat-label">Active Users</div>
            <div class="stat-change positive">
                <i class="fas fa-percentage mr-1"></i>
                {% widthratio active_users total_users 100 %}% active
            </div>
        </div>

        <!-- Verified Users Card -->
        <div class="enhanced-stat-card">
            <div class="stat-icon bg-gradient-to-br from-purple-500 to-purple-600 text-white">
                <i class="fas fa-shield-check"></i>
            </div>
            <div class="stat-value">{{ verified_users|default:0 }}</div>
            <div class="stat-label">Verified Users</div>
            <div class="stat-change positive">
                <i class="fas fa-percentage mr-1"></i>
                {% widthratio verified_users total_users 100 %}% verified
            </div>
        </div>

        <!-- Role Distribution Card -->
        <div class="enhanced-stat-card">
            <div class="stat-icon bg-gradient-to-br from-red-500 to-red-600 text-white">
                <i class="fas fa-chart-pie"></i>
            </div>
            <div class="stat-value">{{ vendors|default:0 }}</div>
            <div class="stat-label">Vendors</div>
            <div class="stat-change">
                <i class="fas fa-users mr-1"></i>
                {{ customers|default:0 }} customers, {{ admins|default:0 }} admins
            </div>
        </div>
    </div>

    <!-- Enhanced Search and Filters -->
    <div class="enhanced-search-container">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <!-- Search Bar -->
            <div class="flex-1 lg:max-w-md">
                <div class="search-input-group">
                    <input
                        type="text"
                        id="search-input"
                        name="search"
                        value="{{ search_query }}"
                        placeholder="Search users by name, email, or phone..."
                        class="search-input"
                        hx-get="{% url 'core:admin_users_search' %}"
                        hx-target="#users-table-container"
                        hx-trigger="keyup changed delay:500ms"
                        hx-include="[name='role'], [name='status']"
                        hx-indicator="#loading-indicator">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center space-x-3">
                <!-- Refresh Button -->
                <button
                    type="button"
                    class="refresh-btn"
                    hx-get="{% url 'core:admin_users_refresh' %}"
                    hx-include="[name='role'], [name='status'], [name='search']"
                    hx-indicator="#loading-indicator"
                    title="Refresh User List">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh
                </button>

                <!-- Export Buttons -->
                <a href="?export=csv{% if search_query %}&search={{ search_query }}{% endif %}{% if current_role_filter %}&role={{ current_role_filter }}{% endif %}{% if current_status_filter %}&status={{ current_status_filter }}{% endif %}"
                   class="export-btn export-btn-csv"
                   title="Export to CSV">
                    <i class="fas fa-file-csv mr-2"></i>
                    CSV
                </a>

                <a href="?export=excel{% if search_query %}&search={{ search_query }}{% endif %}{% if current_role_filter %}&role={{ current_role_filter }}{% endif %}{% if current_status_filter %}&status={{ current_status_filter }}{% endif %}"
                   class="export-btn export-btn-excel"
                   title="Export to Excel">
                    <i class="fas fa-file-excel mr-2"></i>
                    Excel
                </a>
            </div>
        </div>

        <!-- Filter Controls -->
        <div class="filter-container">
            <select
                name="role"
                class="filter-select {% if current_role_filter and current_role_filter != 'all' %}active{% endif %}"
                hx-get="{% url 'core:admin_users_search' %}"
                hx-target="#users-table-container"
                hx-include="[name='search'], [name='status']"
                hx-indicator="#loading-indicator">
                <option value="all">All Roles</option>
                <option value="customer" {% if current_role_filter == 'customer' %}selected{% endif %}>Customers</option>
                <option value="vendor" {% if current_role_filter == 'vendor' %}selected{% endif %}>Vendors</option>
                <option value="admin" {% if current_role_filter == 'admin' %}selected{% endif %}>Administrators</option>
            </select>

            <select
                name="status"
                class="filter-select {% if current_status_filter and current_status_filter != 'all' %}active{% endif %}"
                hx-get="{% url 'core:admin_users_search' %}"
                hx-target="#users-table-container"
                hx-include="[name='search'], [name='role']"
                hx-indicator="#loading-indicator">
                <option value="all">All Status</option>
                <option value="active" {% if current_status_filter == 'active' %}selected{% endif %}>Active</option>
                <option value="inactive" {% if current_status_filter == 'inactive' %}selected{% endif %}>Inactive</option>
                <option value="verified" {% if current_status_filter == 'verified' %}selected{% endif %}>Verified</option>
                <option value="unverified" {% if current_status_filter == 'unverified' %}selected{% endif %}>Unverified</option>
            </select>

            <!-- Clear Filters -->
            {% if search_query or current_role_filter or current_status_filter %}
                <a href="{% url 'core:admin_users' %}"
                   class="modern-action-btn delete-btn"
                   title="Clear All Filters">
                    <i class="fas fa-times mr-1"></i>
                    Clear Filters
                </a>
            {% endif %}
        </div>
    </div>

    <!-- Bulk Actions -->
    <div id="bulkActions" class="hidden bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-xl p-4 mb-6 shadow-md">
        <div class="flex items-center justify-between">
            <span class="text-blue-800 font-semibold font-montserrat">
                <i class="fas fa-check-square mr-2"></i>
                <span id="selectedCount">0</span> users selected
            </span>
            <div class="flex items-center space-x-3">
                <button
                    type="button"
                    class="modern-action-btn activate-btn"
                    onclick="performBulkAction('activate')">
                    <i class="fas fa-user-check mr-1"></i>
                    Activate
                </button>
                <button
                    type="button"
                    class="modern-action-btn deactivate-btn"
                    onclick="performBulkAction('deactivate')">
                    <i class="fas fa-user-slash mr-1"></i>
                    Deactivate
                </button>
                <button
                    type="button"
                    class="modern-action-btn delete-btn"
                    onclick="performBulkAction('delete')">
                    <i class="fas fa-trash mr-1"></i>
                    Delete
                </button>
            </div>
        </div>
    </div>

    <!-- Enhanced Users Table Container -->
    <div class="dashboard-card">
        <div id="users-table-container">
            {% include 'core/dashboard/partials/admin_users_table.html' %}
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/htmx.org@1.9.10"></script>
<script>
// Enhanced User Management JavaScript with HTMX Integration

// Bulk actions functionality
function selectAllUsers() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('input[name="selected_users"]');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateBulkActions();
}

function updateBulkActions() {
    const checkboxes = document.querySelectorAll('input[name="selected_users"]:checked');
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');

    selectedCount.textContent = checkboxes.length;

    if (checkboxes.length > 0) {
        bulkActions.classList.remove('hidden');
        bulkActions.style.animation = 'fadeInUp 0.3s ease-out';
    } else {
        bulkActions.classList.add('hidden');
    }
}

function performBulkAction(action) {
    const checkboxes = document.querySelectorAll('input[name="selected_users"]:checked');
    const userIds = Array.from(checkboxes).map(cb => cb.value);

    if (userIds.length === 0) {
        showToast('Please select users first.', 'warning');
        return;
    }

    let message = '';
    let confirmClass = 'btn-danger';

    switch(action) {
        case 'activate':
            message = `Are you sure you want to activate ${userIds.length} user(s)?`;
            confirmClass = 'btn-success';
            break;
        case 'deactivate':
            message = `Are you sure you want to deactivate ${userIds.length} user(s)?`;
            confirmClass = 'btn-warning';
            break;
        case 'delete':
            message = `Are you sure you want to delete ${userIds.length} user(s)? This action cannot be undone.`;
            confirmClass = 'btn-danger';
            break;
    }

    if (confirm(message)) {
        showToast(`Processing ${action} for ${userIds.length} user(s)...`, 'info');

        // TODO: Implement actual bulk action API call
        console.log(`Performing ${action} on users:`, userIds);
    }
}

// Individual user actions
function toggleUserStatus(userId, action) {
    const actionText = action === 'activate' ? 'activate' : 'deactivate';

    if (confirm(`Are you sure you want to ${actionText} this user?`)) {
        showToast(`${actionText.charAt(0).toUpperCase() + actionText.slice(1)}ing user...`, 'info');

        // TODO: Implement actual API call
        console.log(`${actionText} user:`, userId);

        // Simulate success
        setTimeout(() => {
            showToast(`User ${actionText}d successfully`, 'success');
            htmx.trigger('#users-table-container', 'refresh');
        }, 1000);
    }
}

function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        showToast('Deleting user...', 'info');

        // TODO: Implement actual API call
        console.log('Deleting user:', userId);

        // Simulate success
        setTimeout(() => {
            showToast('User deleted successfully', 'success');
            const row = document.getElementById(`user-${userId}`);
            if (row) {
                row.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => {
                    htmx.trigger('#users-table-container', 'refresh');
                }, 300);
            }
        }, 1000);
    }
}

// Utility functions
function getCsrfToken() {
    const token = document.querySelector('[name=csrfmiddlewaretoken]');
    return token ? token.value : '';
}

function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white font-medium transition-all duration-300 transform translate-x-full`;

    // Set background color based on type
    switch(type) {
        case 'success':
            toast.classList.add('bg-green-500');
            break;
        case 'error':
            toast.classList.add('bg-red-500');
            break;
        case 'warning':
            toast.classList.add('bg-yellow-500');
            break;
        default:
            toast.classList.add('bg-blue-500');
    }

    toast.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : type === 'warning' ? 'exclamation' : 'info'}-circle mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 5000);
}

// HTMX event listeners
document.addEventListener('htmx:beforeRequest', function(event) {
    const indicator = document.getElementById('loading-indicator');
    if (indicator) {
        indicator.style.display = 'flex';
    }

    // Show loading toast
    showToast('Loading...', 'info');
});

document.addEventListener('htmx:afterRequest', function(event) {
    const indicator = document.getElementById('loading-indicator');
    if (indicator) {
        indicator.style.display = 'none';
    }

    // Re-initialize event listeners after HTMX update
    setTimeout(() => {
        initializeEventListeners();
        updateBulkActions();
    }, 100);
});

document.addEventListener('htmx:responseError', function(event) {
    showToast('An error occurred while loading data', 'error');
});

// Initialize event listeners function
function initializeEventListeners() {
    // Re-add event listeners to action buttons
    const actionButtons = document.querySelectorAll('.modern-action-btn');
    actionButtons.forEach(button => {
        button.removeEventListener('click', handleButtonClick);
        button.addEventListener('click', handleButtonClick);
    });

    // Re-add event listeners to pagination buttons
    const paginationButtons = document.querySelectorAll('.modern-pagination-btn');
    paginationButtons.forEach(button => {
        button.removeEventListener('click', handlePaginationClick);
        button.addEventListener('click', handlePaginationClick);
    });
}

// Button click handler
function handleButtonClick(e) {
    e.stopPropagation();
    // Allow the button's normal behavior
}

// Pagination click handler
function handlePaginationClick(e) {
    e.stopPropagation();
    // Allow the link's normal behavior
}



// Dropdown functionality
function toggleDropdown(userId) {
    event.stopPropagation();
    const dropdown = document.getElementById(`dropdown-${userId}`);
    const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');

    // Close all other dropdowns
    allDropdowns.forEach(d => {
        if (d !== dropdown) {
            d.classList.add('hidden');
        }
    });

    // Toggle current dropdown
    if (dropdown) {
        dropdown.classList.toggle('hidden');
    }
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    // Check if click is outside dropdown area
    if (!event.target.closest('.dropdown-toggle') && !event.target.closest('[id^="dropdown-"]')) {
        const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
        allDropdowns.forEach(dropdown => {
            dropdown.classList.add('hidden');
        });
    }
});

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize event listeners
    initializeEventListeners();

    // Initialize search input enhancements
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('focus', function() {
            this.parentElement.classList.add('search-focused');
        });

        searchInput.addEventListener('blur', function() {
            this.parentElement.classList.remove('search-focused');
        });
    }

    // Force enable pointer events on all buttons
    const allButtons = document.querySelectorAll('button, a.modern-action-btn, a.modern-pagination-btn');
    allButtons.forEach(button => {
        button.style.pointerEvents = 'auto';
        button.style.zIndex = '100';
        button.style.position = 'relative';
    });

    // Debug: Log button count
    console.log(`Initialized ${allButtons.length} clickable elements`);

    // Add click test for debugging
    allButtons.forEach((button, index) => {
        button.addEventListener('click', function(e) {
            console.log(`Button ${index} clicked:`, this);
        });
    });
});

// Clear filters function
function clearFilters() {
    const searchInput = document.getElementById('search-input');
    const roleSelect = document.querySelector('select[name="role"]');
    const statusSelect = document.querySelector('select[name="status"]');

    if (searchInput) searchInput.value = '';
    if (roleSelect) roleSelect.value = 'all';
    if (statusSelect) statusSelect.value = 'all';

    // Trigger search to refresh table
    if (searchInput) {
        htmx.trigger(searchInput, 'keyup');
    }

    showToast('Filters cleared', 'info');
}

// Keyboard shortcuts
document.addEventListener('keydown', function(event) {
    // Ctrl/Cmd + R to refresh
    if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
        event.preventDefault();
        htmx.trigger('#users-table-container', 'refresh');
        showToast('Refreshing user list...', 'info');
    }

    // Escape to clear search and close dropdowns
    if (event.key === 'Escape') {
        const searchInput = document.getElementById('search-input');
        if (searchInput && searchInput.value) {
            searchInput.value = '';
            htmx.trigger(searchInput, 'keyup');
        }

        // Close all dropdowns
        const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
        allDropdowns.forEach(dropdown => {
            dropdown.classList.add('hidden');
        });
    }
});

// Animation styles
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes fadeOut {
        from { opacity: 1; transform: translateX(0); }
        to { opacity: 0; transform: translateX(-20px); }
    }

    .search-focused .search-icon {
        color: #dc2626;
        transform: scale(1.1);
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
