{% extends 'base_dashboard.html' %}
{% load static %}
{% load core_extras %}

{% block dashboard_title %}My Car Listings{% endblock %}
{% block page_title %}My Car Listings{% endblock %}

{% block extra_css %}
<style>
    .listing-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
    }
    .listing-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    .status-badge {
        animation: pulse 2s infinite;
    }
    .stats-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .filter-button {
        transition: all 0.3s ease;
    }
    .filter-button:hover {
        transform: translateY(-1px);
    }
    .search-input {
        transition: all 0.3s ease;
    }
    .search-input:focus {
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    }
</style>
{% endblock %}

{% block dashboard_content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
    <!-- Header Section -->
    <div class="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
        <div class="px-6 py-4">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div class="mb-4 lg:mb-0">
                    <h1 class="text-3xl font-bold text-harrier-dark font-montserrat">My Car Listings</h1>
                    <p class="text-gray-600 mt-1">Manage your vehicle inventory and track performance</p>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-3">
                    <button class="inline-flex items-center px-4 py-2 bg-harrier-red text-white rounded-lg hover:bg-red-700 transition-all duration-300 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                        <i class="fas fa-plus mr-2"></i>
                        Add New Car
                    </button>
                    <button class="inline-flex items-center px-4 py-2 bg-white text-harrier-dark border border-gray-300 rounded-lg hover:bg-gray-50 transition-all duration-300 font-medium">
                        <i class="fas fa-download mr-2"></i>
                        Export Data
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="px-6 py-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Cars -->
            <div class="stats-card rounded-xl p-6 shadow-lg">
                <div class="flex items-center">
                    <div class="p-3 bg-harrier-red/10 rounded-lg">
                        <i class="fas fa-car text-harrier-red text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Cars</p>
                        <p class="text-2xl font-bold text-harrier-dark">{{ total_cars }}</p>
                    </div>
                </div>
            </div>

            <!-- Approved Cars -->
            <div class="stats-card rounded-xl p-6 shadow-lg">
                <div class="flex items-center">
                    <div class="p-3 bg-green-100 rounded-lg">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Approved</p>
                        <p class="text-2xl font-bold text-green-600">{{ approved_cars }}</p>
                    </div>
                </div>
            </div>

            <!-- Pending Cars -->
            <div class="stats-card rounded-xl p-6 shadow-lg">
                <div class="flex items-center">
                    <div class="p-3 bg-yellow-100 rounded-lg">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pending</p>
                        <p class="text-2xl font-bold text-yellow-600">{{ pending_cars }}</p>
                    </div>
                </div>
            </div>

            <!-- Total Views -->
            <div class="stats-card rounded-xl p-6 shadow-lg">
                <div class="flex items-center">
                    <div class="p-3 bg-blue-100 rounded-lg">
                        <i class="fas fa-eye text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Views</p>
                        <p class="text-2xl font-bold text-blue-600">{{ total_views|default:0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search Section -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg p-6 mb-8 border border-gray-200">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <!-- Search Bar -->
                <div class="flex-1 max-w-md">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text"
                               class="search-input block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-harrier-red focus:border-harrier-red"
                               placeholder="Search cars by title, brand, or model..."
                               value="{{ search }}"
                               id="searchInput">
                    </div>
                </div>

                <!-- Filter Buttons -->
                <div class="flex flex-wrap gap-2">
                    <button class="filter-button px-4 py-2 bg-harrier-red text-white rounded-lg font-medium shadow-md hover:shadow-lg">
                        <i class="fas fa-list mr-2"></i>All Cars
                    </button>
                    <button class="filter-button px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-lg font-medium hover:bg-gray-50">
                        <i class="fas fa-check-circle mr-2"></i>Approved
                    </button>
                    <button class="filter-button px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-lg font-medium hover:bg-gray-50">
                        <i class="fas fa-clock mr-2"></i>Pending
                    </button>
                    <button class="filter-button px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-lg font-medium hover:bg-gray-50">
                        <i class="fas fa-star mr-2"></i>Featured
                    </button>
                </div>

                <!-- Sort Dropdown -->
                <div class="relative">
                    <select class="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-3 pr-8 focus:outline-none focus:ring-1 focus:ring-harrier-red focus:border-harrier-red">
                        <option value="-created_at">Newest First</option>
                        <option value="created_at">Oldest First</option>
                        <option value="-price">Price: High to Low</option>
                        <option value="price">Price: Low to High</option>
                        <option value="-year">Year: Newest</option>
                        <option value="year">Year: Oldest</option>
                    </select>
                    <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Car Listings Grid -->
        {% if cars %}
            <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
                {% for car in cars %}
                    <div class="listing-card rounded-xl shadow-lg overflow-hidden border border-gray-200">
                        <!-- Car Image -->
                        <div class="relative h-48 bg-gradient-to-br from-gray-200 to-gray-300">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <i class="fas fa-car text-4xl text-gray-400"></i>
                            </div>

                            <!-- Status Badge -->
                            <div class="absolute top-3 left-3">
                                {% if car.is_approved %}
                                    <span class="status-badge inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-green-100 text-green-800 border border-green-200">
                                        <i class="fas fa-check-circle mr-1"></i>Approved
                                    </span>
                                {% else %}
                                    <span class="status-badge inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-yellow-100 text-yellow-800 border border-yellow-200">
                                        <i class="fas fa-clock mr-1"></i>Pending
                                    </span>
                                {% endif %}
                            </div>

                            <!-- Action Menu -->
                            <div class="absolute top-3 right-3">
                                <div class="relative">
                                    <button class="p-2 bg-white/80 backdrop-blur-sm rounded-full shadow-md hover:bg-white transition-all duration-300">
                                        <i class="fas fa-ellipsis-v text-gray-600"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Car Details -->
                        <div class="p-6">
                            <!-- Title and Year -->
                            <div class="mb-4">
                                <h3 class="text-lg font-bold text-harrier-dark font-montserrat mb-1 line-clamp-2">
                                    {{ car.title|default:"Untitled Vehicle" }}
                                </h3>
                                <p class="text-sm text-gray-600">
                                    <i class="fas fa-calendar mr-1"></i>{{ car.year|default:"Year N/A" }}
                                </p>
                            </div>

                            <!-- Price -->
                            <div class="mb-4">
                                <p class="text-2xl font-bold text-harrier-red font-montserrat">
                                    KSH {{ car.price|floatformat:0|default:"0" }}
                                </p>
                            </div>

                            <!-- Quick Stats -->
                            <div class="grid grid-cols-3 gap-3 mb-4">
                                <div class="bg-gray-50 rounded-lg p-3 text-center">
                                    <div class="text-xs text-gray-600 mb-1">Views</div>
                                    <div class="font-bold text-harrier-dark">0</div>
                                </div>
                                <div class="bg-gray-50 rounded-lg p-3 text-center">
                                    <div class="text-xs text-gray-600 mb-1">Inquiries</div>
                                    <div class="font-bold text-harrier-dark">0</div>
                                </div>
                                <div class="bg-gray-50 rounded-lg p-3 text-center">
                                    <div class="text-xs text-gray-600 mb-1">Status</div>
                                    <div class="font-bold text-harrier-dark text-xs">{{ car.status|default:"Available"|title }}</div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex gap-2">
                                <button class="flex-1 bg-harrier-red text-white py-2 px-3 rounded-lg font-medium hover:bg-red-700 transition-all duration-300 text-sm">
                                    <i class="fas fa-edit mr-1"></i>Edit
                                </button>
                                <button class="flex-1 bg-harrier-dark text-white py-2 px-3 rounded-lg font-medium hover:bg-gray-800 transition-all duration-300 text-sm">
                                    <i class="fas fa-eye mr-1"></i>View
                                </button>
                                <button class="bg-gray-200 text-gray-700 py-2 px-3 rounded-lg font-medium hover:bg-gray-300 transition-all duration-300 text-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            <div class="flex justify-center mt-8">
                <nav class="flex items-center space-x-2">
                    <button class="px-3 py-2 bg-white border border-gray-300 rounded-lg text-gray-500 hover:bg-gray-50 transition-all duration-300">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="px-4 py-2 bg-harrier-red text-white rounded-lg font-medium">1</button>
                    <button class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-all duration-300">2</button>
                    <button class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-all duration-300">3</button>
                    <button class="px-3 py-2 bg-white border border-gray-300 rounded-lg text-gray-500 hover:bg-gray-50 transition-all duration-300">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </nav>
            </div>
        {% else %}
            <!-- Empty State -->
            <div class="text-center py-16">
                <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg p-12 max-w-md mx-auto border border-gray-200">
                    <div class="mb-6">
                        <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-car text-3xl text-gray-400"></i>
                        </div>
                        <h3 class="text-xl font-bold text-harrier-dark font-montserrat mb-2">No Cars Listed Yet</h3>
                        <p class="text-gray-600 mb-6">Start building your inventory by adding your first vehicle listing.</p>
                    </div>

                    <button class="inline-flex items-center px-6 py-3 bg-harrier-red text-white rounded-lg hover:bg-red-700 transition-all duration-300 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                        <i class="fas fa-plus mr-2"></i>
                        Add Your First Car
                    </button>

                    <div class="mt-6 text-sm text-gray-500">
                        <p>Need help? <a href="#" class="text-harrier-red hover:underline">View our listing guide</a></p>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- JavaScript for Enhanced Functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    let searchTimeout;

    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            // Implement search functionality here
            console.log('Searching for:', this.value);
        }, 300);
    });

    // Filter buttons
    const filterButtons = document.querySelectorAll('.filter-button');
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active state from all buttons
            filterButtons.forEach(btn => {
                btn.classList.remove('ring-2', 'ring-harrier-red', 'ring-opacity-50');
            });

            // Add active state to clicked button
            this.classList.add('ring-2', 'ring-harrier-red', 'ring-opacity-50');

            // Implement filter functionality here
            console.log('Filter clicked:', this.textContent.trim());
        });
    });

    // Sort dropdown
    const sortSelect = document.querySelector('select');
    sortSelect.addEventListener('change', function() {
        // Implement sort functionality here
        console.log('Sort changed to:', this.value);
    });

    // Card hover effects
    const cards = document.querySelectorAll('.listing-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Action button handlers
    document.addEventListener('click', function(e) {
        if (e.target.closest('button')) {
            const button = e.target.closest('button');
            const buttonText = button.textContent.trim();

            if (buttonText.includes('Edit')) {
                console.log('Edit car clicked');
                // Implement edit functionality
            } else if (buttonText.includes('View')) {
                console.log('View car clicked');
                // Implement view functionality
            } else if (button.querySelector('.fa-trash')) {
                console.log('Delete car clicked');
                // Implement delete functionality with confirmation
                if (confirm('Are you sure you want to delete this car listing?')) {
                    // Delete logic here
                }
            }
        }
    });

    // Smooth animations on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe all cards for animation
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});
</script>
{% endblock %}
