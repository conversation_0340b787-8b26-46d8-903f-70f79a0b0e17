<!-- Vendor Edit Spare Part Modal -->
<div class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-[1500] p-4"
     x-data="{ show: true }"
     x-show="show"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     @click.self="show = false; setTimeout(() => $el.remove(), 200)">
    
    <div class="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
         x-show="show"
         x-transition:enter="transition ease-out duration-300 transform"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-200 transform"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95">
        
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                        <i class="fas fa-edit text-white text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-white font-montserrat">Edit Spare Part</h3>
                        <p class="text-white text-opacity-80 text-sm font-raleway">Update spare part information</p>
                    </div>
                </div>
                <button type="button"
                        class="text-white hover:text-gray-200 transition-colors p-2 hover:bg-white hover:bg-opacity-10 rounded-lg"
                        @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
        
        <!-- Modal Body -->
        <div class="bg-gradient-to-br from-gray-50 to-white px-6 py-6 max-h-[70vh] overflow-y-auto modal-body">
            <!-- Error Alert Container -->
            <div id="form-errors" class="hidden mb-6">
                <div class="bg-red-50 border border-red-200 rounded-xl p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-red-400 text-lg"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-bold text-red-800 font-montserrat">Please correct the following errors:</h3>
                            <div id="error-list" class="mt-2 text-sm text-red-700 font-raleway">
                                <!-- Errors will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Success Alert Container -->
            <div id="form-success" class="hidden mb-6">
                <div class="bg-green-50 border border-green-200 rounded-xl p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-400 text-lg"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-bold text-green-800 font-montserrat">Success!</h3>
                            <p id="success-message" class="mt-1 text-sm text-green-700 font-raleway">
                                <!-- Success message will be populated here -->
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <form id="edit-spare-part-form"
                  hx-post="{% url 'core:vendor_spare_part_edit' part_id=spare_part.id %}"
                  hx-target="#dynamic-content"
                  hx-swap="outerHTML"
                  hx-on::before-request="handleEditFormSubmit()"
                  hx-on::after-request="handleEditFormResponse(event)"
                  class="space-y-6"
                  x-data="{ isSubmitting: false }"
                  @submit="isSubmitting = true"
                  novalidate>
                {% csrf_token %}
                
                <!-- Basic Information Section -->
                <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                    <h4 class="text-lg font-bold text-harrier-dark mb-4 font-montserrat flex items-center">
                        <i class="fas fa-info-circle mr-2 text-harrier-red"></i>Basic Information
                    </h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Part Name -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Part Name <span class="text-harrier-red">*</span>
                            </label>
                            <input type="text" name="name" value="{{ spare_part.name }}" required
                                   minlength="2" maxlength="200"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="Enter part name"
                                   data-validation="required|min:2|max:200">
                            <div class="field-error hidden text-sm text-red-600 font-raleway mt-1"></div>
                        </div>
                        
                        <!-- Part Number -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Part Number
                            </label>
                            <input type="text" name="part_number" value="{{ spare_part.part_number }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="Enter part number">
                        </div>
                        
                        <!-- SKU -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                SKU <span class="text-harrier-red">*</span>
                            </label>
                            <input type="text" name="sku" value="{{ spare_part.sku }}" required
                                   minlength="2" maxlength="50"
                                   pattern="[A-Za-z0-9\-_]+"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="Enter SKU (letters, numbers, hyphens, underscores only)"
                                   data-validation="required|min:2|max:50|alphanumeric">
                            <div class="field-error hidden text-sm text-red-600 font-raleway mt-1"></div>
                        </div>
                        
                        <!-- Barcode -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Barcode
                            </label>
                            <input type="text" name="barcode" value="{{ spare_part.barcode }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="Enter barcode">
                        </div>
                        
                        <!-- Category -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Category <span class="text-harrier-red">*</span>
                            </label>
                            <select name="category_new" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway">
                                <option value="">Select Category</option>
                                {% for category in categories %}
                                    <option value="{{ category.id }}" {% if spare_part.category_new_id == category.id %}selected{% endif %}>{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <!-- Supplier -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Supplier
                            </label>
                            <select name="supplier"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway">
                                <option value="">Select Supplier (Optional)</option>
                                {% for supplier in suppliers %}
                                    <option value="{{ supplier.id }}" {% if spare_part.supplier_id == supplier.id %}selected{% endif %}>{{ supplier.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Pricing & Inventory Section -->
                <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                    <h4 class="text-lg font-bold text-harrier-dark mb-4 font-montserrat flex items-center">
                        <i class="fas fa-dollar-sign mr-2 text-green-600"></i>Pricing & Inventory
                    </h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Price -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Selling Price (KSh) <span class="text-harrier-red">*</span>
                            </label>
                            <input type="number" name="price" value="{{ spare_part.price }}" step="0.01" min="0" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="0.00">
                        </div>
                        
                        <!-- Cost Price -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Cost Price (KSh)
                            </label>
                            <input type="number" name="cost_price" value="{{ spare_part.cost_price }}" step="0.01" min="0"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="0.00">
                        </div>
                        
                        <!-- Discount Price -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Discount Price (KSh)
                            </label>
                            <input type="number" name="discount_price" value="{{ spare_part.discount_price }}" step="0.01" min="0"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="0.00">
                        </div>
                        
                        <!-- Stock Quantity -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Stock Quantity <span class="text-harrier-red">*</span>
                            </label>
                            <input type="number" name="stock_quantity" value="{{ spare_part.stock_quantity }}" min="0" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="0">
                        </div>
                        
                        <!-- Minimum Stock -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Minimum Stock
                            </label>
                            <input type="number" name="minimum_stock" value="{{ spare_part.minimum_stock }}" min="0"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="0">
                        </div>
                        
                        <!-- Maximum Stock -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Maximum Stock
                            </label>
                            <input type="number" name="maximum_stock" value="{{ spare_part.maximum_stock }}" min="0"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="0">
                        </div>
                    </div>
                </div>
                
                <!-- Description & Details Section -->
                <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                    <h4 class="text-lg font-bold text-harrier-dark mb-4 font-montserrat flex items-center">
                        <i class="fas fa-file-alt mr-2 text-blue-600"></i>Description & Details
                    </h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Description -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Description
                            </label>
                            <textarea name="description" rows="4"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway resize-none"
                                      placeholder="Enter part description">{{ spare_part.description }}</textarea>
                        </div>
                        
                        <!-- Specifications -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Specifications
                            </label>
                            <textarea name="specifications" rows="4"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway resize-none"
                                      placeholder="Enter technical specifications">{{ spare_part.specifications }}</textarea>
                        </div>
                        
                        <!-- Condition -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Condition <span class="text-harrier-red">*</span>
                            </label>
                            <select name="condition" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway">
                                <option value="">Select Condition</option>
                                <option value="new" {% if spare_part.condition == 'new' %}selected{% endif %}>New</option>
                                <option value="used" {% if spare_part.condition == 'used' %}selected{% endif %}>Used</option>
                                <option value="refurbished" {% if spare_part.condition == 'refurbished' %}selected{% endif %}>Refurbished</option>
                            </select>
                        </div>
                        
                        <!-- Unit -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Unit
                            </label>
                            <select name="unit"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway">
                                <option value="piece" {% if spare_part.unit == 'piece' %}selected{% endif %}>Piece</option>
                                <option value="set" {% if spare_part.unit == 'set' %}selected{% endif %}>Set</option>
                                <option value="pair" {% if spare_part.unit == 'pair' %}selected{% endif %}>Pair</option>
                                <option value="kit" {% if spare_part.unit == 'kit' %}selected{% endif %}>Kit</option>
                                <option value="liter" {% if spare_part.unit == 'liter' %}selected{% endif %}>Liter</option>
                                <option value="meter" {% if spare_part.unit == 'meter' %}selected{% endif %}>Meter</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Current Image Display -->
                {% if spare_part.main_image %}
                <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                    <h4 class="text-lg font-bold text-harrier-dark mb-4 font-montserrat flex items-center">
                        <i class="fas fa-image mr-2 text-purple-600"></i>Current Image
                    </h4>
                    <div class="flex items-center space-x-4">
                        <img src="{{ spare_part.main_image.url }}" alt="{{ spare_part.name }}" class="w-20 h-20 object-cover rounded-lg border border-gray-200">
                        <div>
                            <p class="text-sm font-medium text-gray-700">Current image</p>
                            <p class="text-xs text-gray-500">Upload a new image to replace this one</p>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- Image Upload Section -->
                <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                    <h4 class="text-lg font-bold text-harrier-dark mb-4 font-montserrat flex items-center">
                        <i class="fas fa-image mr-2 text-purple-600"></i>{% if spare_part.main_image %}Update{% else %}Add{% endif %} Product Image
                    </h4>
                    
                    <div class="space-y-2">
                        <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                            Main Image
                        </label>
                        <input type="file" name="main_image" accept="image/*"
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway">
                        <p class="text-sm text-gray-500 font-raleway">Upload a high-quality image of the spare part (JPG, PNG, max 5MB)</p>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row items-center justify-end space-y-3 sm:space-y-0 sm:space-x-4 pt-6 border-t border-gray-200">
                    <button type="button"
                            class="w-full sm:w-auto px-6 py-3 bg-gray-100 text-gray-700 rounded-xl font-bold hover:bg-gray-200 transition-all duration-200 font-montserrat"
                            @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                        <i class="fas fa-times mr-2"></i>Cancel
                    </button>
                    <button type="submit"
                            class="w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl font-bold hover:from-blue-700 hover:to-blue-800 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl font-montserrat"
                            :disabled="isSubmitting"
                            :class="{ 'opacity-50 cursor-not-allowed': isSubmitting }">
                        <span x-show="!isSubmitting" class="flex items-center">
                            <i class="fas fa-save mr-2"></i>Update Spare Part
                        </span>
                        <span x-show="isSubmitting" class="flex items-center">
                            <i class="fas fa-spinner fa-spin mr-2"></i>Updating...
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Form validation and error handling for Edit Spare Part Modal
function handleEditFormSubmit() {
    // Clear previous errors
    clearEditFormErrors();

    // Validate form before submission
    const form = document.getElementById('edit-spare-part-form');
    const isValid = validateEditForm(form);

    if (!isValid) {
        // Prevent form submission if validation fails
        return false;
    }

    // Show loading state
    showEditFormLoading(true);
    return true;
}

function handleEditFormResponse(event) {
    const xhr = event.detail.xhr;
    const response = event.detail.xhr.response;

    // Hide loading state
    showEditFormLoading(false);

    try {
        // Try to parse JSON response
        const data = JSON.parse(response);

        if (data.success) {
            // Handle success
            showEditFormSuccess(data.message || 'Spare part updated successfully!');

            // Close modal after 2 seconds
            setTimeout(() => {
                const modal = document.querySelector('.fixed');
                if (modal) {
                    modal.remove();
                }

                // Refresh the parts list
                if (typeof refreshPartsList === 'function') {
                    refreshPartsList();
                }
            }, 2000);

        } else if (data.errors) {
            // Handle validation errors
            showEditFormErrors(data.errors);
        } else {
            // Handle general error
            showEditFormErrors({ '__all__': [data.message || 'An error occurred while updating the spare part.'] });
        }
    } catch (e) {
        // Handle non-JSON response (likely HTML error page)
        if (xhr.status >= 400) {
            showEditFormErrors({ '__all__': [`Server error (${xhr.status}): Please try again later.`] });
        } else {
            // Assume success if we can't parse the response but status is OK
            showEditFormSuccess('Spare part updated successfully!');
            setTimeout(() => {
                const modal = document.querySelector('.fixed');
                if (modal) modal.remove();
                if (typeof refreshPartsList === 'function') refreshPartsList();
            }, 1500);
        }
    }
}

function validateEditForm(form) {
    let isValid = true;
    const fields = form.querySelectorAll('[data-validation]');

    fields.forEach(field => {
        const validation = field.getAttribute('data-validation');
        const rules = validation.split('|');
        const value = field.value.trim();

        let fieldErrors = [];

        // Check each validation rule
        rules.forEach(rule => {
            if (rule === 'required' && !value) {
                fieldErrors.push('This field is required.');
            } else if (rule.startsWith('min:')) {
                const min = parseFloat(rule.split(':')[1]);
                if (field.type === 'number' && parseFloat(value) < min) {
                    fieldErrors.push(`Value must be at least ${min}.`);
                } else if (field.type === 'text' && value.length < min) {
                    fieldErrors.push(`Must be at least ${min} characters long.`);
                }
            } else if (rule.startsWith('max:')) {
                const max = parseFloat(rule.split(':')[1]);
                if (field.type === 'number' && parseFloat(value) > max) {
                    fieldErrors.push(`Value must not exceed ${max}.`);
                } else if (field.type === 'text' && value.length > max) {
                    fieldErrors.push(`Must not exceed ${max} characters.`);
                }
            } else if (rule === 'alphanumeric' && value && !/^[A-Za-z0-9\-_]+$/.test(value)) {
                fieldErrors.push('Only letters, numbers, hyphens, and underscores are allowed.');
            }
        });

        // Show field-specific errors
        if (fieldErrors.length > 0) {
            showEditFieldError(field, fieldErrors[0]);
            isValid = false;
        } else {
            clearEditFieldError(field);
        }
    });

    return isValid;
}

function showEditFieldError(field, message) {
    const errorDiv = field.parentNode.querySelector('.field-error');
    if (errorDiv) {
        errorDiv.textContent = message;
        errorDiv.classList.remove('hidden');
    }

    // Add error styling to field
    field.classList.add('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
    field.classList.remove('border-gray-300', 'focus:ring-harrier-red', 'focus:border-harrier-red');
}

function clearEditFieldError(field) {
    const errorDiv = field.parentNode.querySelector('.field-error');
    if (errorDiv) {
        errorDiv.classList.add('hidden');
    }

    // Remove error styling from field
    field.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
    field.classList.add('border-gray-300', 'focus:ring-harrier-red', 'focus:border-harrier-red');
}

function clearEditFormErrors() {
    // Hide main error container
    const errorContainer = document.getElementById('form-errors');
    if (errorContainer) {
        errorContainer.classList.add('hidden');
    }

    // Hide success container
    const successContainer = document.getElementById('form-success');
    if (successContainer) {
        successContainer.classList.add('hidden');
    }

    // Clear all field errors
    const form = document.getElementById('edit-spare-part-form');
    const fields = form.querySelectorAll('[data-validation]');
    fields.forEach(field => clearEditFieldError(field));
}

function showEditFormErrors(errors) {
    const errorContainer = document.getElementById('form-errors');
    const errorList = document.getElementById('error-list');

    if (!errorContainer || !errorList) return;

    // Clear previous errors
    errorList.innerHTML = '';

    // Add new errors
    const errorMessages = [];

    Object.keys(errors).forEach(field => {
        const fieldErrors = Array.isArray(errors[field]) ? errors[field] : [errors[field]];
        fieldErrors.forEach(error => {
            if (field === '__all__') {
                errorMessages.push(error);
            } else {
                // Try to find the field and show inline error
                const fieldElement = document.querySelector(`[name="${field}"]`);
                if (fieldElement) {
                    showEditFieldError(fieldElement, error);
                } else {
                    errorMessages.push(`${field}: ${error}`);
                }
            }
        });
    });

    // Show general errors
    if (errorMessages.length > 0) {
        errorMessages.forEach(message => {
            const li = document.createElement('div');
            li.textContent = `• ${message}`;
            li.className = 'mb-1';
            errorList.appendChild(li);
        });

        errorContainer.classList.remove('hidden');

        // Scroll to top of modal to show errors
        const modalBody = document.querySelector('.modal-body');
        if (modalBody) {
            modalBody.scrollTop = 0;
        }
    }
}

function showEditFormSuccess(message) {
    const successContainer = document.getElementById('form-success');
    const successMessage = document.getElementById('success-message');

    if (successContainer && successMessage) {
        successMessage.textContent = message;
        successContainer.classList.remove('hidden');

        // Scroll to top to show success message
        const modalBody = document.querySelector('.modal-body');
        if (modalBody) {
            modalBody.scrollTop = 0;
        }
    }
}

function showEditFormLoading(show) {
    const submitButton = document.querySelector('#edit-spare-part-form button[type="submit"]');
    if (submitButton) {
        if (show) {
            submitButton.disabled = true;
            submitButton.classList.add('opacity-50', 'cursor-not-allowed');
        } else {
            submitButton.disabled = false;
            submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
        }
    }
}
</script>
