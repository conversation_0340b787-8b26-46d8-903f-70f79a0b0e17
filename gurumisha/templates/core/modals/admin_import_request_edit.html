{% load static %}

<!-- Edit Import Request Modal -->
<div class="fixed inset-0 z-[1500] overflow-y-auto"
     id="edit-import-request-modal"
     x-data="{ show: false }"
     x-init="show = true"
     x-show="show"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">

    <!-- Modal Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity modal-backdrop z-[1400]"
         @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)"></div>
    
    <!-- Modal Container -->
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        
        <!-- Modal Panel -->
        <div class="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full modal-panel"
             x-show="show"
             x-transition:enter="ease-out duration-400"
             x-transition:enter-start="opacity-0 translate-y-8 sm:translate-y-0 sm:scale-90"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-300"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-8 sm:translate-y-0 sm:scale-90">
            
            <!-- Modal Header -->
            <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-5">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-white bg-opacity-20 backdrop-blur-sm rounded-xl flex items-center justify-center mr-4 shadow-lg">
                            <i class="fas fa-edit text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-white font-montserrat">
                                Edit Import Request
                            </h3>
                            <p class="text-sm text-white text-opacity-90 font-raleway">Request #{{ import_request.id|stringformat:"05d" }} • {{ import_request.customer.get_full_name|default:import_request.customer.username }}</p>
                        </div>
                    </div>

                    <!-- Status Badge and Close Button -->
                    <div class="flex items-center space-x-4">
                        <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-xl px-4 py-2">
                            <span class="text-white font-semibold text-sm flex items-center">
                                <div class="w-3 h-3 bg-white rounded-full mr-2"></div>
                                {{ import_request.get_status_display }}
                            </span>
                        </div>

                        <!-- Close Button -->
                        <button type="button"
                                class="text-white text-opacity-70 hover:text-white hover:bg-white hover:bg-opacity-20 rounded-lg p-2 transition-all duration-200"
                                @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                            <span class="sr-only">Close</span>
                            <i class="fas fa-times text-lg"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Modal Body -->
            <div class="bg-gradient-to-br from-gray-50 to-white px-6 py-6 max-h-96 overflow-y-auto modal-body">
                <form id="edit-import-request-form"
                      hx-post="{% url 'core:admin_import_request_edit' import_request.id %}"
                      hx-target="#import-requests-table"
                      hx-swap="outerHTML"
                      hx-on::after-request="if(event.detail.successful) { show = false; setTimeout(() => $el.closest('.fixed').remove(), 200); }"
                      class="space-y-6"
                      x-data="{ isSubmitting: false }"
                      @submit="isSubmitting = true">
                    {% csrf_token %}
                    
                    <!-- Customer and Status -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="customer" class="block text-sm font-medium text-gray-700 mb-2">
                                Customer <span class="text-red-500">*</span>
                            </label>
                            <select name="customer" id="customer" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">
                                {% for user in customers %}
                                <option value="{{ user.id }}" {% if user.id == import_request.customer.id %}selected{% endif %}>
                                    {{ user.get_full_name|default:user.username }} ({{ user.email }})
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                                Status <span class="text-red-500">*</span>
                            </label>
                            <select name="status" id="status" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">
                                {% for value, label in status_choices %}
                                <option value="{{ value }}" {% if value == import_request.status %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <!-- Car Details -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="brand" class="block text-sm font-medium text-gray-700 mb-2">
                                Brand <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="brand" id="brand" required value="{{ import_request.brand }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">
                        </div>
                        
                        <div>
                            <label for="model" class="block text-sm font-medium text-gray-700 mb-2">
                                Model <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="model" id="model" required value="{{ import_request.model }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">
                        </div>
                        
                        <div>
                            <label for="year" class="block text-sm font-medium text-gray-700 mb-2">
                                Year <span class="text-red-500">*</span>
                            </label>
                            <input type="number" name="year" id="year" required min="1990" max="2025" value="{{ import_request.year }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">
                        </div>
                    </div>
                    
                    <!-- Origin and Color -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="origin_country" class="block text-sm font-medium text-gray-700 mb-2">
                                Origin Country <span class="text-red-500">*</span>
                            </label>
                            <select name="origin_country" id="origin_country" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">
                                <option value="Japan" {% if import_request.origin_country == 'Japan' %}selected{% endif %}>Japan</option>
                                <option value="UK" {% if import_request.origin_country == 'UK' %}selected{% endif %}>United Kingdom</option>
                                <option value="Germany" {% if import_request.origin_country == 'Germany' %}selected{% endif %}>Germany</option>
                                <option value="USA" {% if import_request.origin_country == 'USA' %}selected{% endif %}>United States</option>
                                <option value="South Korea" {% if import_request.origin_country == 'South Korea' %}selected{% endif %}>South Korea</option>
                                <option value="Other" {% if import_request.origin_country == 'Other' %}selected{% endif %}>Other</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="preferred_color" class="block text-sm font-medium text-gray-700 mb-2">
                                Preferred Color
                            </label>
                            <input type="text" name="preferred_color" id="preferred_color" value="{{ import_request.preferred_color }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">
                        </div>
                    </div>
                    
                    <!-- Budget Range -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="budget_min" class="block text-sm font-medium text-gray-700 mb-2">
                                Minimum Budget (KES) <span class="text-red-500">*</span>
                            </label>
                            <input type="number" name="budget_min" id="budget_min" required min="0" step="1000" value="{{ import_request.budget_min }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">
                        </div>
                        
                        <div>
                            <label for="budget_max" class="block text-sm font-medium text-gray-700 mb-2">
                                Maximum Budget (KES) <span class="text-red-500">*</span>
                            </label>
                            <input type="number" name="budget_max" id="budget_max" required min="0" step="1000" value="{{ import_request.budget_max }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">
                        </div>
                    </div>
                    
                    <!-- Estimated Cost and Delivery -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="estimated_cost" class="block text-sm font-medium text-gray-700 mb-2">
                                Estimated Cost (KES)
                            </label>
                            <input type="number" name="estimated_cost" id="estimated_cost" min="0" step="1000" value="{{ import_request.estimated_cost }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">
                        </div>
                        
                        <div>
                            <label for="estimated_delivery" class="block text-sm font-medium text-gray-700 mb-2">
                                Estimated Delivery Date
                            </label>
                            <input type="date" name="estimated_delivery" id="estimated_delivery" value="{{ import_request.estimated_delivery|date:'Y-m-d' }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">
                        </div>
                    </div>
                    
                    <!-- Tracking Number -->
                    <div>
                        <label for="tracking_number" class="block text-sm font-medium text-gray-700 mb-2">
                            Tracking Number
                        </label>
                        <input type="text" name="tracking_number" id="tracking_number" value="{{ import_request.tracking_number }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200"
                               placeholder="Enter tracking number if available">
                    </div>
                    
                    <!-- Special Requirements -->
                    <div>
                        <label for="special_requirements" class="block text-sm font-medium text-gray-700 mb-2">
                            Special Requirements
                        </label>
                        <textarea name="special_requirements" id="special_requirements" rows="3"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">{{ import_request.special_requirements }}</textarea>
                    </div>
                    
                    <!-- Admin Notes -->
                    <div>
                        <label for="admin_notes" class="block text-sm font-medium text-gray-700 mb-2">
                            Admin Notes
                        </label>
                        <textarea name="admin_notes" id="admin_notes" rows="2"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">{{ import_request.admin_notes }}</textarea>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-center mt-6 pt-6 border-t border-gray-200">
                        <!-- Cancel Button -->
                        <button type="button"
                                class="enhanced-btn enhanced-btn-cancel"
                                @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                            <i class="fas fa-times mr-2"></i>
                            <span>Cancel</span>
                        </button>

                        <!-- Save Button -->
                        <button type="submit"
                                class="enhanced-btn enhanced-btn-save"
                                :disabled="isSubmitting">
                            <i class="fas fa-save mr-2" x-show="!isSubmitting"></i>
                            <i class="fas fa-spinner fa-spin mr-2" x-show="isSubmitting"></i>
                            <span x-text="isSubmitting ? 'Saving Changes...' : 'Save Changes'"></span>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Enhanced Modal Footer -->
            <div class="bg-gradient-to-r from-gray-50 via-white to-gray-50 px-6 py-6 border-t border-gray-200">
                <!-- Info Section -->
                <div class="flex items-center justify-center mb-6">
                    <div class="flex items-center text-sm text-gray-600 bg-amber-50 px-4 py-2 rounded-lg border border-amber-200">
                        <div class="w-5 h-5 bg-amber-500 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-info text-white text-xs"></i>
                        </div>
                        <span class="font-medium font-raleway">Changes will be saved and customer will be notified</span>
                    </div>
                </div>

                <!-- Additional Info -->
                <div class="mt-4 text-center">
                    <p class="text-xs text-gray-500 font-raleway">
                        Customer will receive an email notification about the changes
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Enhanced Modal Styles */
    .modal-panel {
        backdrop-filter: blur(20px);
        background: rgba(255, 255, 255, 0.98);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    /* Harrier Color System */
    :root {
        --harrier-red-50: #fef2f2;
        --harrier-red-400: #f87171;
        --harrier-red-500: #ef4444;
        --harrier-red-600: #dc2626;
        --harrier-red-700: #b91c1c;
        --harrier-red-800: #991b1b;

        --harrier-dark-50: #f9fafb;
        --harrier-dark-100: #f3f4f6;
        --harrier-dark-200: #e5e7eb;
        --harrier-dark-300: #d1d5db;
        --harrier-dark-400: #9ca3af;
        --harrier-dark-600: #4b5563;
        --harrier-dark-700: #374151;
        --harrier-dark-800: #1f2937;

        --harrier-blue-400: #60a5fa;
        --harrier-blue-500: #3b82f6;
        --harrier-blue-600: #2563eb;
        --harrier-blue-700: #1d4ed8;
        --harrier-blue-800: #1e40af;
    }

    /* Simplified Enhanced Button Base Styles */
    .enhanced-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 14px;
        line-height: 1.2;
        border-radius: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateY(0);
        font-family: 'Montserrat', sans-serif;
        min-width: 120px;
        max-width: 180px;
        height: 44px;
        border: 2px solid transparent;
        cursor: pointer;
        user-select: none;
        position: relative;
        overflow: visible;
        white-space: nowrap;
        box-sizing: border-box;
    }

    .enhanced-btn::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        padding: 2px;
        background: linear-gradient(135deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1), rgba(0,0,0,0.1));
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: xor;
        -webkit-mask-composite: xor;
        pointer-events: none;
    }

    .enhanced-btn:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(var(--harrier-blue-500), 0.3);
    }

    /* Button Content Layout */
    .btn-content {
        @apply relative z-10 flex items-center justify-center;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .btn-icon-wrapper {
        @apply w-6 h-6 flex items-center justify-center mr-3;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .btn-icon {
        @apply text-lg;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .btn-text {
        @apply font-semibold;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Simplified Cancel Button */
    .enhanced-btn-cancel {
        background: linear-gradient(135deg, #f9fafb, #f3f4f6);
        color: #374151;
        border: 2px solid #e5e7eb;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .enhanced-btn-cancel:hover {
        background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
        color: #1f2937;
        border-color: #d1d5db;
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    }

    .enhanced-btn-cancel:hover .btn-icon {
        transform: rotate(90deg);
    }

    .enhanced-btn-cancel:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(156, 163, 175, 0.3);
    }

    .enhanced-btn-cancel:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    /* Simplified Save Button */
    .enhanced-btn-save {
        background: linear-gradient(135deg, #2563eb, #3b82f6, #1d4ed8);
        background-size: 200% 100%;
        background-position: 0% 50%;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
    }

    .enhanced-btn-save:hover {
        background-position: 100% 50%;
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(59, 130, 246, 0.5);
    }

    .enhanced-btn-save:hover i {
        transform: scale(1.2) rotate(360deg);
    }

    .enhanced-btn-save:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.4);
    }

    .enhanced-btn-save:active {
        transform: translateY(-1px);
        box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
    }

    .enhanced-btn-save:disabled {
        opacity: 0.8;
        cursor: not-allowed;
        transform: none !important;
        background: linear-gradient(135deg, #1d4ed8, #1e40af);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
    }

    /* Texture Layers with Fixed Z-Index */
    .btn-texture {
        position: absolute;
        inset: 0;
        border-radius: inherit;
        opacity: 0.3;
        background-image:
            radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        mix-blend-mode: overlay;
        pointer-events: none;
        z-index: 1;
    }

    .btn-noise {
        position: absolute;
        inset: 0;
        border-radius: inherit;
        opacity: 0.02;
        background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
        mix-blend-mode: multiply;
        pointer-events: none;
        z-index: 2;
    }






    /* Button Content with Fixed Visibility */
    .btn-content {
        @apply relative flex items-center justify-center;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 50;
    }

    .btn-icon-wrapper {
        @apply w-6 h-6 flex items-center justify-center mr-3 rounded-full;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        z-index: 51;
    }

    .btn-icon {
        @apply text-base;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 52;
    }

    .btn-text {
        @apply font-semibold tracking-wide;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        letter-spacing: 0.025em;
        z-index: 52;
    }

    /* Pulse Glow Effect for Save Button */
    .btn-glow {
        position: absolute;
        inset: -2px;
        border-radius: calc(1rem + 2px);
        background: linear-gradient(135deg,
            var(--harrier-blue-400),
            var(--harrier-blue-600),
            var(--harrier-blue-800));
        opacity: 0;
        filter: blur(8px);
        transition: opacity 0.4s ease;
        z-index: -1;
    }

    .enhanced-btn-save:hover .btn-glow {
        opacity: 0.6;
        animation: pulse-glow 2s ease-in-out infinite;
    }

    @keyframes pulse-glow {
        0%, 100% {
            opacity: 0.6;
            transform: scale(1);
        }
        50% {
            opacity: 0.8;
            transform: scale(1.02);
        }
    }

    /* Enhanced Ripple Effect with Fixed Z-Index */
    .btn-ripple {
        position: absolute;
        inset: 0;
        border-radius: inherit;
        background: radial-gradient(circle,
            rgba(255, 255, 255, 0.4) 0%,
            rgba(255, 255, 255, 0.2) 30%,
            transparent 70%);
        transform: scale(0);
        opacity: 0;
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 10;
    }

    .enhanced-btn:active .btn-ripple {
        transform: scale(4);
        opacity: 1;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Enhanced Shine Effect with Fixed Z-Index */
    .btn-shine {
        position: absolute;
        inset: 0;
        border-radius: inherit;
        background: linear-gradient(45deg,
            transparent 30%,
            rgba(255, 255, 255, 0.3) 50%,
            transparent 70%);
        transform: translateX(-100%);
        transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 20;
    }

    .enhanced-btn-submit:hover .btn-shine,
    .enhanced-btn-save:hover .btn-shine {
        transform: translateX(100%);
    }

    /* Submit Button Styles (for consistency across modals) */
    .enhanced-btn-submit {
        background: linear-gradient(135deg,
            var(--harrier-red-600) 0%,
            var(--harrier-red-500) 15%,
            var(--harrier-red-700) 35%,
            var(--harrier-dark-800) 65%,
            var(--harrier-dark-700) 85%,
            var(--harrier-red-800) 100%);
        background-size: 300% 100%;
        background-position: 0% 50%;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow:
            0 8px 24px rgba(220, 38, 38, 0.4),
            0 4px 12px rgba(31, 41, 55, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2),
            inset 0 -1px 0 rgba(0, 0, 0, 0.2);
        position: relative;
    }

    .enhanced-btn-submit::after {
        content: '';
        position: absolute;
        inset: 1px;
        border-radius: calc(1rem - 1px);
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.15) 0%,
            rgba(255, 255, 255, 0.05) 30%,
            transparent 50%,
            rgba(0, 0, 0, 0.05) 70%,
            rgba(0, 0, 0, 0.1) 100%);
        pointer-events: none;
        z-index: 1;
    }

    .enhanced-btn-submit:hover {
        background-position: 100% 50%;
        transform: translateY(-4px);
        box-shadow:
            0 16px 40px rgba(220, 38, 38, 0.5),
            0 8px 20px rgba(31, 41, 55, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            inset 0 -1px 0 rgba(0, 0, 0, 0.3);
        border-color: rgba(255, 255, 255, 0.2);
    }

    .enhanced-btn-submit:hover .btn-icon {
        transform: scale(1.3) rotate(180deg);
    }

    .enhanced-btn-submit:hover .btn-icon-wrapper {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    /* Loading State Animation */
    .btn-loading .btn-icon {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    /* Mobile Responsiveness */
    @media (max-width: 640px) {
        .enhanced-btn {
            @apply px-6 py-3 text-sm;
            min-width: 140px;
        }

        .btn-icon-wrapper {
            @apply w-5 h-5 mr-2;
        }

        .btn-icon {
            @apply text-base;
        }
    }

    /* Accessibility Enhancements */
    @media (prefers-reduced-motion: reduce) {
        .enhanced-btn,
        .btn-content,
        .btn-icon-wrapper,
        .btn-icon,
        .btn-text,
        .btn-ripple,
        .btn-shine {
            transition: none !important;
            animation: none !important;
        }

        .enhanced-btn:hover {
            transform: none !important;
        }
    }

    .modal-btn-primary {
        @apply bg-gradient-to-r from-blue-600 to-blue-700 text-white;
        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    }

    .modal-btn-primary:hover {
        @apply from-blue-700 to-blue-800;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    }

    .modal-btn-primary:disabled {
        @apply opacity-70 cursor-not-allowed;
        transform: none !important;
        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2) !important;
    }

    .modal-btn-secondary {
        @apply bg-white text-gray-700 border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .modal-btn-secondary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .modal-panel {
            margin: 1rem;
            max-width: calc(100vw - 2rem);
        }

        .modal-btn {
            @apply px-4 py-2 text-xs;
        }
    }

    /* Loading states */
    .htmx-request .modal-btn {
        @apply opacity-50 cursor-wait;
    }

    .htmx-request .modal-btn i {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Accessibility */
    .modal-btn:focus {
        outline: 2px solid rgba(59, 130, 246, 0.5);
        outline-offset: 2px;
    }
</style>
