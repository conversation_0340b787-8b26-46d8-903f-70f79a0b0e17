{% load static %}

<!-- Enhanced Message Preview Modal with Harrier Design -->
<div id="message-preview-modal" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity backdrop-blur-sm" aria-hidden="true" onclick="closeModal('message-preview-modal')"></div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full modal-panel">
            <!-- Modal Header -->
            <div class="bg-gradient-to-r from-harrier-red to-harrier-dark px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-eye text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-white font-montserrat" id="modal-title">
                                Message Preview
                            </h3>
                            <p class="text-sm text-white/80 font-raleway">
                                Preview how this message will appear to users
                            </p>
                        </div>
                    </div>
                    <button type="button" 
                            class="text-white/80 hover:text-white transition-colors duration-200 p-2 hover:bg-white/10 rounded-lg"
                            onclick="closeModal('message-preview-modal')">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>

            <!-- Modal Body -->
            <div class="px-6 py-6">
                <!-- Message Info -->
                <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                            <span class="font-semibold text-gray-700">Type:</span>
                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ message.get_message_type_display }}
                            </span>
                        </div>
                        <div>
                            <span class="font-semibold text-gray-700">Audience:</span>
                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {{ message.get_target_audience_display }}
                            </span>
                        </div>
                        <div>
                            <span class="font-semibold text-gray-700">Status:</span>
                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if message.status == 'active' %}bg-green-100 text-green-800
                                {% elif message.status == 'scheduled' %}bg-yellow-100 text-yellow-800
                                {% elif message.status == 'draft' %}bg-gray-100 text-gray-800
                                {% elif message.status == 'paused' %}bg-orange-100 text-orange-800
                                {% elif message.status == 'expired' %}bg-red-100 text-red-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ message.get_status_display }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Preview Tabs -->
                <div class="mb-6">
                    <div class="border-b border-gray-200">
                        <nav class="-mb-px flex space-x-8">
                            <button class="preview-tab active border-b-2 border-harrier-red text-harrier-red py-2 px-1 text-sm font-medium" 
                                    data-tab="popup">
                                <i class="fas fa-window-restore mr-2"></i>Popup Preview
                            </button>
                            <button class="preview-tab border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium" 
                                    data-tab="banner">
                                <i class="fas fa-flag mr-2"></i>Banner Preview
                            </button>
                            <button class="preview-tab border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium" 
                                    data-tab="dashboard">
                                <i class="fas fa-tachometer-alt mr-2"></i>Dashboard Preview
                            </button>
                        </nav>
                    </div>
                </div>

                <!-- Preview Content -->
                <div id="preview-content">
                    <!-- Popup Preview -->
                    <div id="popup-preview" class="preview-panel">
                        <div class="bg-gray-100 p-6 rounded-lg">
                            <p class="text-sm text-gray-600 mb-4">Popup Modal Preview:</p>
                            <div class="bg-white rounded-lg shadow-lg max-w-md mx-auto overflow-hidden"
                                 style="background-color: {{ message.background_color|default:'#ffffff' }}; color: {{ message.text_color|default:'#000000' }};">
                                
                                <!-- Message Header -->
                                <div class="p-4 border-b border-gray-200">
                                    <div class="flex items-center">
                                        {% if message.icon_class %}
                                            <div class="w-8 h-8 rounded-lg flex items-center justify-center mr-3"
                                                 style="background-color: {{ message.action_button_color|default:'#dc2626' }};">
                                                <i class="{{ message.icon_class }} text-white text-sm"></i>
                                            </div>
                                        {% endif %}
                                        <h3 class="font-bold text-lg font-montserrat">{{ message.title }}</h3>
                                    </div>
                                </div>
                                
                                <!-- Message Body -->
                                <div class="p-4">
                                    {% if message.featured_image %}
                                        <img src="{{ message.featured_image.url }}" 
                                             alt="{{ message.featured_image_alt }}" 
                                             class="w-full h-32 object-cover rounded-lg mb-4">
                                    {% endif %}
                                    
                                    <div class="message-content font-raleway">
                                        {{ message.content|safe }}
                                    </div>
                                    
                                    {% if message.action_button_text and message.action_button_url %}
                                        <div class="mt-4 flex justify-end">
                                            <button class="px-4 py-2 rounded-lg text-white font-semibold text-sm"
                                                    style="background-color: {{ message.action_button_color|default:'#dc2626' }};">
                                                {{ message.action_button_text }}
                                            </button>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Banner Preview -->
                    <div id="banner-preview" class="preview-panel hidden">
                        <div class="bg-gray-100 p-6 rounded-lg">
                            <p class="text-sm text-gray-600 mb-4">Banner Notification Preview:</p>
                            <div class="rounded-lg shadow-sm border-l-4 p-4"
                                 style="background-color: {{ message.background_color|default:'#ffffff' }}; 
                                        color: {{ message.text_color|default:'#000000' }};
                                        border-left-color: {{ message.action_button_color|default:'#dc2626' }};">
                                <div class="flex items-start">
                                    {% if message.icon_class %}
                                        <div class="flex-shrink-0 mr-3">
                                            <i class="{{ message.icon_class }} text-lg" 
                                               style="color: {{ message.action_button_color|default:'#dc2626' }};"></i>
                                        </div>
                                    {% endif %}
                                    <div class="flex-1">
                                        <h4 class="font-bold font-montserrat">{{ message.title }}</h4>
                                        {% if message.excerpt %}
                                            <p class="text-sm mt-1 font-raleway">{{ message.excerpt }}</p>
                                        {% endif %}
                                        {% if message.action_button_text and message.action_button_url %}
                                            <button class="mt-2 text-sm font-semibold underline"
                                                    style="color: {{ message.action_button_color|default:'#dc2626' }};">
                                                {{ message.action_button_text }}
                                            </button>
                                        {% endif %}
                                    </div>
                                    <button class="flex-shrink-0 ml-3 text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Dashboard Preview -->
                    <div id="dashboard-preview" class="preview-panel hidden">
                        <div class="bg-gray-100 p-6 rounded-lg">
                            <p class="text-sm text-gray-600 mb-4">Dashboard Card Preview:</p>
                            <div class="bg-white rounded-lg shadow-sm border p-4"
                                 style="background-color: {{ message.background_color|default:'#ffffff' }}; color: {{ message.text_color|default:'#000000' }};">
                                <div class="flex items-start">
                                    {% if message.icon_class %}
                                        <div class="w-10 h-10 rounded-lg flex items-center justify-center mr-4"
                                             style="background-color: {{ message.action_button_color|default:'#dc2626' }};">
                                            <i class="{{ message.icon_class }} text-white"></i>
                                        </div>
                                    {% endif %}
                                    <div class="flex-1">
                                        <h3 class="font-bold text-lg font-montserrat mb-2">{{ message.title }}</h3>
                                        {% if message.excerpt %}
                                            <p class="text-sm mb-3 font-raleway">{{ message.excerpt }}</p>
                                        {% endif %}
                                        {% if message.action_button_text and message.action_button_url %}
                                            <button class="px-3 py-1 rounded text-white text-sm font-semibold"
                                                    style="background-color: {{ message.action_button_color|default:'#dc2626' }};">
                                                {{ message.action_button_text }}
                                            </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Message Details -->
                <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Performance Stats -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-800 mb-3 font-montserrat">Performance</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Total Views:</span>
                                <span class="font-semibold">{{ message.total_views|default:0 }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Total Clicks:</span>
                                <span class="font-semibold">{{ message.total_clicks|default:0 }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Click Rate:</span>
                                <span class="font-semibold">{{ message.click_through_rate|floatformat:1 }}%</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Dismissals:</span>
                                <span class="font-semibold">{{ message.total_dismissals|default:0 }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Message Settings -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-800 mb-3 font-montserrat">Settings</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Priority:</span>
                                <span class="font-semibold">{{ message.get_priority_display }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Show as Popup:</span>
                                <span class="font-semibold">{% if message.show_as_popup %}Yes{% else %}No{% endif %}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Show as Banner:</span>
                                <span class="font-semibold">{% if message.show_as_banner %}Yes{% else %}No{% endif %}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Show in Dashboard:</span>
                                <span class="font-semibold">{% if message.show_in_dashboard %}Yes{% else %}No{% endif %}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Scheduling Info -->
                {% if message.publication_date or message.expiration_date %}
                <div class="mt-6 bg-blue-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-blue-800 mb-3 font-montserrat">
                        <i class="fas fa-calendar-alt mr-2"></i>Scheduling
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        {% if message.publication_date %}
                        <div>
                            <span class="text-blue-600 font-semibold">Publication Date:</span>
                            <div class="text-blue-800">{{ message.publication_date|date:"M d, Y H:i" }}</div>
                        </div>
                        {% endif %}
                        {% if message.expiration_date %}
                        <div>
                            <span class="text-blue-600 font-semibold">Expiration Date:</span>
                            <div class="text-blue-800">{{ message.expiration_date|date:"M d, Y H:i" }}</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Modal Footer -->
            <div class="bg-gray-50 px-6 py-4 flex justify-end">
                <button type="button" 
                        class="enhanced-btn enhanced-btn-cancel"
                        onclick="closeModal('message-preview-modal')">
                    <div class="btn-content">
                        <div class="btn-icon-wrapper">
                            <i class="btn-icon fas fa-times"></i>
                        </div>
                        <span class="btn-text">Close</span>
                    </div>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    /* Modal Styles */
    .modal-panel {
        backdrop-filter: blur(20px);
        background: rgba(255, 255, 255, 0.98);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    /* Preview Tab Styles */
    .preview-tab {
        transition: all 0.3s ease;
    }

    .preview-tab.active {
        border-color: var(--harrier-red) !important;
        color: var(--harrier-red) !important;
    }

    .preview-panel {
        transition: all 0.3s ease;
    }

    .preview-panel.hidden {
        display: none;
    }

    /* Button Styles */
    .enhanced-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 14px;
        line-height: 1.2;
        border-radius: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateY(0);
        font-family: 'Montserrat', sans-serif;
        min-width: 120px;
        height: 44px;
        border: 2px solid transparent;
        cursor: pointer;
        user-select: none;
        position: relative;
        overflow: visible;
        white-space: nowrap;
        box-sizing: border-box;
    }

    .enhanced-btn-cancel {
        background: linear-gradient(135deg, #F3F4F6 0%, #E5E7EB 100%);
        color: #374151;
        border: 1px solid #D1D5DB;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .enhanced-btn-cancel:hover {
        background: linear-gradient(135deg, #E5E7EB 0%, #D1D5DB 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .btn-content {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 2;
    }

    .btn-icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        margin-right: 8px;
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .btn-icon {
        font-size: 14px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .btn-text {
        font-weight: 600;
        letter-spacing: 0.025em;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* CSS Variables */
    :root {
        --harrier-red: #dc2626;
        --harrier-dark: #1f2937;
    }
</style>

<script>
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.remove();
    }
}

// Tab switching functionality
document.addEventListener('click', function(event) {
    if (event.target.closest('.preview-tab')) {
        const clickedTab = event.target.closest('.preview-tab');
        const tabName = clickedTab.dataset.tab;
        
        // Update active state
        document.querySelectorAll('.preview-tab').forEach(tab => {
            tab.classList.remove('active');
            tab.classList.add('border-transparent', 'text-gray-500');
            tab.classList.remove('border-harrier-red', 'text-harrier-red');
        });
        
        clickedTab.classList.add('active');
        clickedTab.classList.remove('border-transparent', 'text-gray-500');
        clickedTab.classList.add('border-harrier-red', 'text-harrier-red');
        
        // Show corresponding preview panel
        document.querySelectorAll('.preview-panel').forEach(panel => {
            panel.classList.add('hidden');
        });
        
        const targetPanel = document.getElementById(tabName + '-preview');
        if (targetPanel) {
            targetPanel.classList.remove('hidden');
        }
    }
});
</script>
