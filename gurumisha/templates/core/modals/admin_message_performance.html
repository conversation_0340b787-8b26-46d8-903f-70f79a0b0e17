{% load static %}

<!-- Enhanced Message Performance Modal with Harrier Design -->
<div id="message-performance-modal" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity backdrop-blur-sm" aria-hidden="true" onclick="closeModal('message-performance-modal')"></div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full modal-panel">
            <!-- Modal Header -->
            <div class="bg-gradient-to-r from-harrier-red to-harrier-dark px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-chart-line text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-white font-montserrat" id="modal-title">
                                Message Performance
                            </h3>
                            <p class="text-sm text-white/80 font-raleway">
                                Detailed analytics for "{{ message.title }}"
                            </p>
                        </div>
                    </div>
                    <button type="button" 
                            class="text-white/80 hover:text-white transition-colors duration-200 p-2 hover:bg-white/10 rounded-lg"
                            onclick="closeModal('message-performance-modal')">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>

            <!-- Modal Body -->
            <div class="px-6 py-6 max-h-[70vh] overflow-y-auto">
                <!-- Message Overview -->
                <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                        <div>
                            <span class="font-semibold text-gray-700">Type:</span>
                            <div class="mt-1">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ message.get_message_type_display }}
                                </span>
                            </div>
                        </div>
                        <div>
                            <span class="font-semibold text-gray-700">Audience:</span>
                            <div class="mt-1">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    {{ message.get_target_audience_display }}
                                </span>
                            </div>
                        </div>
                        <div>
                            <span class="font-semibold text-gray-700">Status:</span>
                            <div class="mt-1">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if message.status == 'active' %}bg-green-100 text-green-800
                                    {% elif message.status == 'scheduled' %}bg-yellow-100 text-yellow-800
                                    {% elif message.status == 'draft' %}bg-gray-100 text-gray-800
                                    {% elif message.status == 'paused' %}bg-orange-100 text-orange-800
                                    {% elif message.status == 'expired' %}bg-red-100 text-red-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ message.get_status_display }}
                                </span>
                            </div>
                        </div>
                        <div>
                            <span class="font-semibold text-gray-700">Created:</span>
                            <div class="mt-1 text-gray-600">{{ message.created_at|date:"M d, Y" }}</div>
                        </div>
                    </div>
                </div>

                <!-- Performance Metrics by Time Period -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                    <!-- 7 Days -->
                    <div class="performance-period-card">
                        <div class="period-header">
                            <h4 class="period-title">Last 7 Days</h4>
                            <i class="fas fa-calendar-week text-blue-500"></i>
                        </div>
                        <div class="period-metrics">
                            <div class="metric-row">
                                <span class="metric-label">Displays:</span>
                                <span class="metric-value">{{ performance_7d.total_displays|default:0 }}</span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">Clicks:</span>
                                <span class="metric-value">{{ performance_7d.total_clicks|default:0 }}</span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">CTR:</span>
                                <span class="metric-value {% if performance_7d.click_through_rate > 5 %}text-green-600{% elif performance_7d.click_through_rate > 2 %}text-yellow-600{% else %}text-gray-600{% endif %}">
                                    {{ performance_7d.click_through_rate }}%
                                </span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">Dismissals:</span>
                                <span class="metric-value">{{ performance_7d.total_dismissals|default:0 }}</span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">Unique Users:</span>
                                <span class="metric-value">{{ performance_7d.unique_users|default:0 }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 30 Days -->
                    <div class="performance-period-card featured">
                        <div class="period-header">
                            <h4 class="period-title">Last 30 Days</h4>
                            <i class="fas fa-calendar-alt text-harrier-red"></i>
                        </div>
                        <div class="period-metrics">
                            <div class="metric-row">
                                <span class="metric-label">Displays:</span>
                                <span class="metric-value">{{ performance_30d.total_displays|default:0 }}</span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">Clicks:</span>
                                <span class="metric-value">{{ performance_30d.total_clicks|default:0 }}</span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">CTR:</span>
                                <span class="metric-value {% if performance_30d.click_through_rate > 5 %}text-green-600{% elif performance_30d.click_through_rate > 2 %}text-yellow-600{% else %}text-gray-600{% endif %}">
                                    {{ performance_30d.click_through_rate }}%
                                </span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">Dismissals:</span>
                                <span class="metric-value">{{ performance_30d.total_dismissals|default:0 }}</span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">Unique Users:</span>
                                <span class="metric-value">{{ performance_30d.unique_users|default:0 }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 90 Days -->
                    <div class="performance-period-card">
                        <div class="period-header">
                            <h4 class="period-title">Last 90 Days</h4>
                            <i class="fas fa-calendar text-purple-500"></i>
                        </div>
                        <div class="period-metrics">
                            <div class="metric-row">
                                <span class="metric-label">Displays:</span>
                                <span class="metric-value">{{ performance_90d.total_displays|default:0 }}</span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">Clicks:</span>
                                <span class="metric-value">{{ performance_90d.total_clicks|default:0 }}</span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">CTR:</span>
                                <span class="metric-value {% if performance_90d.click_through_rate > 5 %}text-green-600{% elif performance_90d.click_through_rate > 2 %}text-yellow-600{% else %}text-gray-600{% endif %}">
                                    {{ performance_90d.click_through_rate }}%
                                </span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">Dismissals:</span>
                                <span class="metric-value">{{ performance_90d.total_dismissals|default:0 }}</span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">Unique Users:</span>
                                <span class="metric-value">{{ performance_90d.unique_users|default:0 }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Insights -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- Engagement Quality -->
                    <div class="insight-card">
                        <div class="insight-header">
                            <h4 class="insight-title">
                                <i class="fas fa-heart mr-2 text-pink-500"></i>
                                Engagement Quality
                            </h4>
                        </div>
                        <div class="insight-content">
                            {% if performance_30d.click_through_rate > 5 %}
                                <div class="insight-item positive">
                                    <i class="fas fa-thumbs-up"></i>
                                    <span>Excellent engagement! Your message has a high click-through rate.</span>
                                </div>
                            {% elif performance_30d.click_through_rate > 2 %}
                                <div class="insight-item neutral">
                                    <i class="fas fa-hand-paper"></i>
                                    <span>Good engagement. Consider optimizing for better performance.</span>
                                </div>
                            {% else %}
                                <div class="insight-item negative">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <span>Low engagement. Consider revising content or targeting.</span>
                                </div>
                            {% endif %}

                            {% if performance_30d.dismissal_rate > 20 %}
                                <div class="insight-item negative">
                                    <i class="fas fa-times-circle"></i>
                                    <span>High dismissal rate. Users may find this message irrelevant.</span>
                                </div>
                            {% elif performance_30d.dismissal_rate > 10 %}
                                <div class="insight-item neutral">
                                    <i class="fas fa-info-circle"></i>
                                    <span>Moderate dismissal rate. Monitor user feedback.</span>
                                </div>
                            {% else %}
                                <div class="insight-item positive">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Low dismissal rate. Users find this message valuable.</span>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Recommendations -->
                    <div class="insight-card">
                        <div class="insight-header">
                            <h4 class="insight-title">
                                <i class="fas fa-lightbulb mr-2 text-yellow-500"></i>
                                Recommendations
                            </h4>
                        </div>
                        <div class="insight-content">
                            {% if performance_30d.total_displays < 100 %}
                                <div class="insight-item">
                                    <i class="fas fa-bullhorn"></i>
                                    <span>Low reach. Consider expanding target audience or increasing priority.</span>
                                </div>
                            {% endif %}

                            {% if performance_30d.click_through_rate < 2 %}
                                <div class="insight-item">
                                    <i class="fas fa-edit"></i>
                                    <span>Improve call-to-action or message content to increase engagement.</span>
                                </div>
                            {% endif %}

                            {% if message.action_button_text and performance_30d.total_clicks == 0 %}
                                <div class="insight-item">
                                    <i class="fas fa-mouse-pointer"></i>
                                    <span>No clicks on action button. Review button text and URL.</span>
                                </div>
                            {% endif %}

                            <div class="insight-item">
                                <i class="fas fa-clock"></i>
                                <span>Best performance typically occurs within first 24 hours of publication.</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Message Settings Summary -->
                <div class="settings-summary">
                    <h4 class="summary-title">
                        <i class="fas fa-cog mr-2 text-gray-600"></i>
                        Message Settings
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="setting-item">
                            <span class="setting-label">Display Options:</span>
                            <div class="setting-value">
                                {% if message.show_as_popup %}
                                    <span class="setting-badge active">Popup</span>
                                {% endif %}
                                {% if message.show_as_banner %}
                                    <span class="setting-badge active">Banner</span>
                                {% endif %}
                                {% if message.show_in_dashboard %}
                                    <span class="setting-badge active">Dashboard</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">Priority:</span>
                            <div class="setting-value">
                                <span class="setting-badge priority-{{ message.priority }}">
                                    {{ message.get_priority_display }}
                                </span>
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">Max Displays:</span>
                            <div class="setting-value">
                                <span class="setting-badge">{{ message.max_displays_per_user }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="bg-gray-50 px-6 py-4 flex flex-col sm:flex-row sm:justify-between gap-3">
                <div class="text-sm text-gray-600">
                    Last updated: {{ message.updated_at|date:"M d, Y H:i" }}
                </div>
                
                <div class="flex items-center gap-3">
                    <button type="button" 
                            class="enhanced-btn enhanced-btn-secondary"
                            hx-get="{% url 'core:admin_message_edit_modal' message.id %}"
                            hx-target="body"
                            hx-swap="beforeend">
                        <div class="btn-content">
                            <div class="btn-icon-wrapper">
                                <i class="btn-icon fas fa-edit"></i>
                            </div>
                            <span class="btn-text">Edit Message</span>
                        </div>
                    </button>
                    
                    <button type="button" 
                            class="enhanced-btn enhanced-btn-cancel"
                            onclick="closeModal('message-performance-modal')">
                        <div class="btn-content">
                            <div class="btn-icon-wrapper">
                                <i class="btn-icon fas fa-times"></i>
                            </div>
                            <span class="btn-text">Close</span>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Performance Modal Styles */
    .modal-panel {
        backdrop-filter: blur(20px);
        background: rgba(255, 255, 255, 0.98);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    .performance-period-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        padding: 1.5rem;
        transition: all 0.3s ease;
    }

    .performance-period-card.featured {
        border: 2px solid var(--harrier-red);
        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.15);
    }

    .period-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .period-title {
        font-size: 1rem;
        font-weight: 700;
        color: #1f2937;
        font-family: 'Montserrat', sans-serif;
    }

    .period-metrics {
        space-y: 0.75rem;
    }

    .metric-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
    }

    .metric-label {
        font-size: 0.875rem;
        color: #6b7280;
        font-family: 'Raleway', sans-serif;
    }

    .metric-value {
        font-size: 0.875rem;
        font-weight: 600;
        color: #1f2937;
        font-family: 'Montserrat', sans-serif;
    }

    .insight-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        padding: 1.5rem;
    }

    .insight-header {
        margin-bottom: 1rem;
    }

    .insight-title {
        font-size: 1rem;
        font-weight: 700;
        color: #1f2937;
        font-family: 'Montserrat', sans-serif;
        display: flex;
        align-items: center;
    }

    .insight-content {
        space-y: 0.75rem;
    }

    .insight-item {
        display: flex;
        align-items: flex-start;
        padding: 0.75rem;
        border-radius: 8px;
        font-size: 0.875rem;
        font-family: 'Raleway', sans-serif;
    }

    .insight-item i {
        margin-right: 0.75rem;
        margin-top: 0.125rem;
        flex-shrink: 0;
    }

    .insight-item.positive {
        background: rgba(16, 185, 129, 0.1);
        color: #065f46;
    }

    .insight-item.neutral {
        background: rgba(245, 158, 11, 0.1);
        color: #92400e;
    }

    .insight-item.negative {
        background: rgba(239, 68, 68, 0.1);
        color: #991b1b;
    }

    .settings-summary {
        background: rgba(249, 250, 251, 0.8);
        border-radius: 12px;
        padding: 1.5rem;
    }

    .summary-title {
        font-size: 1rem;
        font-weight: 700;
        color: #1f2937;
        font-family: 'Montserrat', sans-serif;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    .setting-item {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .setting-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: #6b7280;
        font-family: 'Raleway', sans-serif;
    }

    .setting-value {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .setting-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 6px;
        font-size: 0.75rem;
        font-weight: 600;
        background: #e5e7eb;
        color: #374151;
    }

    .setting-badge.active {
        background: #dbeafe;
        color: #1e40af;
    }

    .setting-badge.priority-4 {
        background: #fee2e2;
        color: #991b1b;
    }

    .setting-badge.priority-3 {
        background: #fed7aa;
        color: #9a3412;
    }

    /* Button Styles */
    .enhanced-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 14px;
        line-height: 1.2;
        border-radius: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        font-family: 'Montserrat', sans-serif;
        min-width: 120px;
        height: 44px;
        border: 2px solid transparent;
        cursor: pointer;
        text-decoration: none;
    }

    .enhanced-btn-cancel {
        background: linear-gradient(135deg, #F3F4F6 0%, #E5E7EB 100%);
        color: #374151;
        border: 1px solid #D1D5DB;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .enhanced-btn-secondary {
        background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    .btn-content {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        margin-right: 8px;
    }

    /* CSS Variables */
    :root {
        --harrier-red: #dc2626;
        --harrier-dark: #1f2937;
    }
</style>

<script>
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.remove();
    }
}
</script>
