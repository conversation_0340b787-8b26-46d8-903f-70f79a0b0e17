{% load static %}

<!-- Comprehensive Location Management Modal -->
<div class="fixed inset-0 z-50 overflow-y-auto" 
     id="location-management-modal"
     x-data="{ show: false, activeTab: 'current' }"
     x-init="show = true"
     x-show="show"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">
    
    <!-- Modal Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity modal-backdrop"
         @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)"></div>

    <!-- Modal Content -->
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="relative bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden"
             x-show="show"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 transform scale-95"
             x-transition:enter-end="opacity-100 transform scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 transform scale-100"
             x-transition:leave-end="opacity-0 transform scale-95">
            
            <!-- Modal Header -->
            <div class="bg-gradient-to-r from-red-900 to-black text-white px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-xl font-semibold font-montserrat">Location Management</h3>
                        <p class="text-red-200 text-sm">{{ import_order.order_number }} - {{ import_order.vehicle_details }}</p>
                    </div>
                    <button @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)"
                            class="text-red-200 hover:text-white transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>

            <!-- Tab Navigation -->
            <div class="border-b border-gray-200 bg-gray-50">
                <nav class="flex space-x-8 px-6">
                    <button @click="activeTab = 'current'"
                            :class="activeTab === 'current' ? 'border-red-500 text-red-600' : 'border-transparent text-gray-500 hover:text-gray-700'"
                            class="py-4 px-1 border-b-2 font-medium text-sm transition-colors">
                        <i class="fas fa-map-marker-alt mr-2"></i>Current Location
                    </button>
                    <button @click="activeTab = 'route'"
                            :class="activeTab === 'route' ? 'border-red-500 text-red-600' : 'border-transparent text-gray-500 hover:text-gray-700'"
                            class="py-4 px-1 border-b-2 font-medium text-sm transition-colors">
                        <i class="fas fa-route mr-2"></i>Route & Waypoints
                    </button>
                    <button @click="activeTab = 'history'"
                            :class="activeTab === 'history' ? 'border-red-500 text-red-600' : 'border-transparent text-gray-500 hover:text-gray-700'"
                            class="py-4 px-1 border-b-2 font-medium text-sm transition-colors">
                        <i class="fas fa-history mr-2"></i>Location History
                    </button>
                    <button @click="activeTab = 'map'"
                            :class="activeTab === 'map' ? 'border-red-500 text-red-600' : 'border-transparent text-gray-500 hover:text-gray-700'"
                            class="py-4 px-1 border-b-2 font-medium text-sm transition-colors">
                        <i class="fas fa-map mr-2"></i>Live Map
                    </button>
                </nav>
            </div>

            <!-- Modal Body -->
            <div class="p-6 max-h-[60vh] overflow-y-auto">
                
                <!-- Current Location Tab -->
                <div x-show="activeTab === 'current'" class="space-y-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Current Location Info -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4 font-montserrat">Current Location</h4>
                            
                            {% if import_order.current_latitude and import_order.current_longitude %}
                            <div class="space-y-3">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                                    <span class="font-medium text-gray-900">{{ import_order.current_location_name|default:"Location Available" }}</span>
                                </div>
                                <div class="text-sm text-gray-600">
                                    <strong>Coordinates:</strong> {{ import_order.current_coordinates_string }}
                                </div>
                                <div class="text-sm text-gray-600">
                                    <strong>Last Updated:</strong> 
                                    {% if import_order.last_location_update %}
                                        {{ import_order.last_location_update|timesince }} ago
                                    {% else %}
                                        Never
                                    {% endif %}
                                </div>
                                {% if import_order.google_maps_url %}
                                <a href="{{ import_order.google_maps_url }}" 
                                   target="_blank" 
                                   class="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm">
                                    <i class="fas fa-external-link-alt mr-1"></i>View on Google Maps
                                </a>
                                {% endif %}
                            </div>
                            {% else %}
                            <div class="text-center py-8">
                                <i class="fas fa-map-marker-alt text-gray-300 text-3xl mb-3"></i>
                                <p class="text-gray-500">No location data available</p>
                                <p class="text-sm text-gray-400">Update the location below</p>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Update Location Form -->
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4 font-montserrat">Update Location</h4>
                            
                            <form hx-post="{% url 'core:admin_update_order_location' import_order.id %}"
                                  hx-target="#tracking-table-container"
                                  hx-swap="innerHTML"
                                  class="space-y-4">
                                {% csrf_token %}
                                
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Latitude</label>
                                        <input type="number" 
                                               name="latitude" 
                                               step="any"
                                               value="{{ import_order.current_latitude|default:'' }}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                                               placeholder="e.g., -1.2921"
                                               required>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Longitude</label>
                                        <input type="number" 
                                               name="longitude" 
                                               step="any"
                                               value="{{ import_order.current_longitude|default:'' }}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                                               placeholder="e.g., 36.8219"
                                               required>
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Location Name</label>
                                    <input type="text" 
                                           name="location_name"
                                           value="{{ import_order.current_location_name|default:'' }}"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                                           placeholder="e.g., Mombasa Port, Customs Office">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                                    <textarea name="notes"
                                              rows="3"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                                              placeholder="Additional notes about this location update..."></textarea>
                                </div>
                                
                                <div class="flex space-x-3">
                                    <button type="submit" 
                                            class="flex-1 btn-harrier-primary">
                                        <i class="fas fa-map-marker-alt mr-2"></i>Update Location
                                    </button>
                                    <button type="button" 
                                            onclick="getCurrentLocation()"
                                            class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                                        <i class="fas fa-crosshairs mr-2"></i>Use My Location
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Route & Waypoints Tab -->
                <div x-show="activeTab === 'route'" class="space-y-6">
                    {% if route %}
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-lg font-semibold text-blue-900">{{ route.route_name }}</h4>
                                <p class="text-blue-700">{{ route.get_route_type_display }} - {{ route.get_route_status_display }}</p>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-blue-900">{{ route.progress_percentage }}%</div>
                                <div class="text-sm text-blue-700">Complete</div>
                            </div>
                        </div>
                    </div>

                    <!-- Waypoints List -->
                    <div class="bg-white border border-gray-200 rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <h4 class="text-lg font-semibold text-gray-900">Waypoints</h4>
                                <button onclick="addWaypoint({{ import_order.id }})" 
                                        class="btn-harrier-secondary">
                                    <i class="fas fa-plus mr-2"></i>Add Waypoint
                                </button>
                            </div>
                        </div>
                        <div class="p-6">
                            {% for waypoint in waypoints %}
                            <div class="flex items-center space-x-4 py-3 {% if not forloop.last %}border-b border-gray-100{% endif %}">
                                <div class="w-8 h-8 rounded-full flex items-center justify-center
                                    {% if waypoint.is_completed %}bg-green-100 text-green-600
                                    {% elif waypoint.is_current %}bg-blue-100 text-blue-600
                                    {% else %}bg-gray-100 text-gray-600{% endif %}">
                                    {{ waypoint.sequence_order }}
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium text-gray-900">{{ waypoint.name }}</div>
                                    <div class="text-sm text-gray-500">{{ waypoint.get_waypoint_type_display }}</div>
                                </div>
                                <div class="text-right">
                                    {% if waypoint.is_completed %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check mr-1"></i>Completed
                                        </span>
                                    {% elif waypoint.is_current %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <i class="fas fa-map-marker-alt mr-1"></i>Current
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            <i class="fas fa-clock mr-1"></i>Pending
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                            {% empty %}
                            <div class="text-center py-8">
                                <i class="fas fa-route text-gray-300 text-3xl mb-3"></i>
                                <p class="text-gray-500">No waypoints defined</p>
                                <button onclick="addWaypoint({{ import_order.id }})" 
                                        class="mt-3 btn-harrier-secondary">
                                    <i class="fas fa-plus mr-2"></i>Add First Waypoint
                                </button>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% else %}
                    <div class="text-center py-12">
                        <i class="fas fa-route text-gray-300 text-4xl mb-4"></i>
                        <h4 class="text-lg font-semibold text-gray-900 mb-2">No Route Defined</h4>
                        <p class="text-gray-600 mb-6">Create a route to track waypoints and progress</p>
                        <button onclick="createRoute({{ import_order.id }})" 
                                class="btn-harrier-primary">
                            <i class="fas fa-plus mr-2"></i>Create Route
                        </button>
                    </div>
                    {% endif %}
                </div>

                <!-- Location History Tab -->
                <div x-show="activeTab === 'history'" class="space-y-4">
                    {% for entry in tracking_history %}
                    <div class="bg-white border border-gray-200 rounded-lg p-4">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start space-x-3">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-map-pin text-blue-600 text-xs"></i>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900">
                                        {{ entry.coordinates_string }}
                                    </div>
                                    <div class="text-sm text-gray-600">
                                        {{ entry.notes|default:"Location update" }}
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        {{ entry.get_tracking_source_display }} • {{ entry.recorded_at|date:"M d, Y H:i" }}
                                    </div>
                                </div>
                            </div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                {{ entry.get_status_at_time_display }}
                            </span>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-12">
                        <i class="fas fa-history text-gray-300 text-4xl mb-4"></i>
                        <h4 class="text-lg font-semibold text-gray-900 mb-2">No Location History</h4>
                        <p class="text-gray-600">Location updates will appear here</p>
                    </div>
                    {% endfor %}
                </div>

                <!-- Live Map Tab -->
                <div x-show="activeTab === 'map'">
                    <div class="bg-gray-100 rounded-lg h-96 flex items-center justify-center">
                        <div class="text-center">
                            <i class="fas fa-map text-gray-400 text-4xl mb-4"></i>
                            <h4 class="text-lg font-semibold text-gray-900 mb-2">Live Map</h4>
                            <p class="text-gray-600 mb-4">Interactive map will be loaded here</p>
                            <a href="/import/tracking/{{ import_order.order_number }}/" 
                               target="_blank" 
                               class="btn-harrier-primary">
                                <i class="fas fa-external-link-alt mr-2"></i>Open Full Map
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3">
                <button @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)"
                        class="btn-harrier-secondary">
                    Close
                </button>
                <button onclick="refreshLocationData({{ import_order.id }})"
                        class="btn-harrier-primary">
                    <i class="fas fa-sync-alt mr-2"></i>Refresh Data
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            document.querySelector('input[name="latitude"]').value = position.coords.latitude;
            document.querySelector('input[name="longitude"]').value = position.coords.longitude;
        });
    } else {
        alert('Geolocation is not supported by this browser.');
    }
}

function addWaypoint(orderId) {
    // Implementation for adding waypoint
    console.log('Add waypoint for order:', orderId);
}

function createRoute(orderId) {
    // Implementation for creating route
    console.log('Create route for order:', orderId);
}

function refreshLocationData(orderId) {
    // Refresh the modal data
    htmx.ajax('GET', `/dashboard/admin/tracking/order/${orderId}/location-modal/`, {
        target: '#location-management-modal',
        swap: 'outerHTML'
    });
}
</script>
