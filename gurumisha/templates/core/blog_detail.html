{% extends 'base.html' %}
{% load static %}

{% block title %}{{ post.title }} - Gurumisha Resources{% endblock %}

{% block meta_description %}{{ post.meta_description|default:post.excerpt|default:post.content|truncatewords:25 }}{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
    /* Revolutionary Article Design System */
    :root {
        --gradient-primary: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
        --gradient-secondary: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        --gradient-accent: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        --shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.08);
        --shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.12);
        --shadow-strong: 0 20px 60px rgba(0, 0, 0, 0.15);
        --border-radius-lg: 20px;
        --border-radius-xl: 24px;
        --animation-spring: cubic-bezier(0.34, 1.56, 0.64, 1);
    }

    /* Enhanced Article Container */
    .article-container {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        min-height: 100vh;
    }

    /* Revolutionary Hero Section */
    .article-hero {
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
        position: relative;
        overflow: hidden;
        padding: 4rem 0 6rem;
    }

    .article-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 20% 80%, rgba(220, 38, 38, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(220, 38, 38, 0.2) 0%, transparent 50%);
        z-index: 0;
    }

    .article-hero::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            linear-gradient(135deg, rgba(220, 38, 38, 0.05) 0%, rgba(15, 23, 42, 0.9) 100%),
            url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        z-index: 1;
    }

    .hero-content {
        position: relative;
        z-index: 2;
        max-width: 4xl;
        margin: 0 auto;
        padding: 0 2rem;
        text-align: center;
    }

    /* Content Type Badge */
    .content-type-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background: rgba(220, 38, 38, 0.9);
        color: white;
        border-radius: 25px;
        font-weight: 700;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 2rem;
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
    }

    /* Enhanced Article Title */
    .article-title {
        font-size: clamp(2.5rem, 6vw, 4rem);
        font-weight: 900;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #e2e8f0 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        line-height: 1.1;
        margin-bottom: 1.5rem;
        font-family: 'Montserrat', sans-serif;
        letter-spacing: -0.02em;
    }

    /* Article Meta */
    .article-meta {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 2rem;
        flex-wrap: wrap;
        color: rgba(255, 255, 255, 0.9);
        font-size: 1rem;
        margin-bottom: 2rem;
        font-family: 'Raleway', sans-serif;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        backdrop-filter: blur(10px);
    }

    /* Author Section */
    .author-section {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        margin-top: 2rem;
        padding: 1.5rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .author-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
        font-size: 1.5rem;
        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
    }

    .author-info h4 {
        color: white;
        font-weight: 700;
        font-size: 1.125rem;
        margin: 0;
        font-family: 'Montserrat', sans-serif;
    }

    .author-info p {
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
        font-size: 0.875rem;
    }

    /* Enhanced Content Section */
    .content-section {
        background: white;
        margin: -3rem 2rem 0;
        border-radius: 30px;
        box-shadow: 0 25px 80px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 3;
        overflow: hidden;
    }

    /* Featured Image */
    .featured-image-container {
        position: relative;
        height: 400px;
        overflow: hidden;
        border-radius: 30px 30px 0 0;
    }

    .featured-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.6s ease;
    }

    .featured-image-container:hover .featured-image {
        transform: scale(1.05);
    }

    /* Content Body */
    .content-body {
        padding: 3rem;
    }

    /* Enhanced Typography */
    .content-body h1,
    .content-body h2,
    .content-body h3,
    .content-body h4,
    .content-body h5,
    .content-body h6 {
        font-family: 'Montserrat', sans-serif;
        font-weight: 700;
        color: #1f2937;
        margin: 2rem 0 1rem;
        line-height: 1.3;
    }

    .content-body h1 {
        font-size: 2.5rem;
        color: #dc2626;
        text-align: center;
        margin-bottom: 2rem;
        position: relative;
    }

    .content-body h1::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 4px;
        background: linear-gradient(90deg, transparent 0%, #dc2626 50%, transparent 100%);
        border-radius: 2px;
    }

    .content-body h2 {
        font-size: 2rem;
        color: #dc2626;
        border-bottom: 3px solid #dc2626;
        padding-bottom: 0.5rem;
        margin-top: 3rem;
    }

    .content-body h3 {
        font-size: 1.5rem;
        color: #374151;
        margin-top: 2.5rem;
    }

    .content-body h4 {
        font-size: 1.25rem;
        color: #4b5563;
    }

    .content-body p {
        font-family: 'Raleway', sans-serif;
        font-size: 1.125rem;
        line-height: 1.8;
        color: #374151;
        margin: 1.5rem 0;
        text-align: justify;
    }

    .content-body ul,
    .content-body ol {
        font-family: 'Raleway', sans-serif;
        font-size: 1.125rem;
        line-height: 1.8;
        color: #374151;
        margin: 2rem 0;
        padding-left: 0;
    }

    .content-body li {
        margin: 1rem 0;
        position: relative;
        padding-left: 2rem;
        list-style: none;
    }

    .content-body ul li::before {
        content: '▶';
        color: #dc2626;
        font-weight: bold;
        position: absolute;
        left: 0;
        top: 0;
    }

    .content-body ol {
        counter-reset: item;
    }

    .content-body ol li::before {
        content: counter(item) '.';
        counter-increment: item;
        color: #dc2626;
        font-weight: bold;
        position: absolute;
        left: 0;
        top: 0;
    }

    /* Enhanced Blockquotes */
    .content-body blockquote {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        border-left: 5px solid #dc2626;
        padding: 2rem;
        margin: 2rem 0;
        border-radius: 0 15px 15px 0;
        font-style: italic;
        font-size: 1.25rem;
        color: #92400e;
        position: relative;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .content-body blockquote::before {
        content: '"';
        font-size: 4rem;
        color: #dc2626;
        position: absolute;
        top: -1rem;
        left: 1rem;
        font-family: serif;
        opacity: 0.7;
    }

    /* Code Blocks */
    .content-body pre,
    .content-body code {
        background: #1f2937;
        color: #f9fafb;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
    }

    .content-body pre {
        padding: 1.5rem;
        overflow-x: auto;
        margin: 2rem 0;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .content-body code {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    /* Tables */
    .content-body table {
        width: 100%;
        border-collapse: collapse;
        margin: 2rem 0;
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .content-body th,
    .content-body td {
        padding: 1rem;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
    }

    .content-body th {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        color: white;
        font-weight: 700;
        font-family: 'Montserrat', sans-serif;
    }

    .content-body tr:hover {
        background: #f9fafb;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .article-hero {
            padding: 2rem 0 4rem;
        }

        .hero-content {
            padding: 0 1rem;
        }

        .article-title {
            font-size: 2rem;
        }

        .article-meta {
            gap: 1rem;
            font-size: 0.875rem;
        }

        .content-section {
            margin: -2rem 1rem 0;
        }

        .content-body {
            padding: 2rem 1.5rem;
        }

        .content-body h1 { font-size: 2rem; }
        .content-body h2 { font-size: 1.5rem; }
        .content-body p { font-size: 1rem; }
    }
</style>
{% endblock %}

{% block content %}
<div class="article-container">
    <!-- Revolutionary Hero Section -->
    <section class="article-hero">
        <div class="hero-content">
            <!-- Content Type Badge -->
            <div class="content-type-badge">
                <i class="fas fa-{{ post.content_type|default:'newspaper' }}"></i>
                <span>{{ post.get_content_type_display|default:'Article' }}</span>
            </div>

            <!-- Article Title -->
            <h1 class="article-title">{{ post.title }}</h1>

            <!-- Article Meta -->
            <div class="article-meta">
                <div class="meta-item">
                    <i class="fas fa-calendar"></i>
                    <span>{{ post.published_at|date:"M d, Y" }}</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-clock"></i>
                    <span>{{ post.estimated_read_time|default:5 }} min read</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-eye"></i>
                    <span>{{ post.views_count|default:0 }} views</span>
                </div>
                {% if post.difficulty_level %}
                <div class="meta-item">
                    <i class="fas fa-signal"></i>
                    <span>{{ post.get_difficulty_level_display }}</span>
                </div>
                {% endif %}
            </div>

            <!-- Author Section -->
            {% if post.author %}
            <div class="author-section">
                <div class="author-avatar">
                    {{ post.author.first_name|first|default:post.author.username|first }}{{ post.author.last_name|first|default:"" }}
                </div>
                <div class="author-info">
                    <h4>{{ post.author.get_full_name|default:post.author.username }}</h4>
                    <p>Author & Automotive Expert</p>
                </div>
            </div>
            {% endif %}
        </div>
    </section>

    <!-- Enhanced Content Section -->
    <section class="content-section">
        <!-- Featured Image -->
        {% if post.featured_image %}
        <div class="featured-image-container">
            <img src="{{ post.featured_image.url }}" alt="{{ post.featured_image_alt|default:post.title }}" class="featured-image">
        </div>
        {% endif %}

        <!-- Content Body -->
        <div class="content-body">
            <!-- Excerpt -->
            {% if post.excerpt %}
            <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); padding: 2rem; border-radius: 15px; margin-bottom: 3rem; border-left: 5px solid #dc2626; font-size: 1.25rem; font-style: italic; color: #92400e; text-align: center; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);">
                {{ post.excerpt }}
            </div>
            {% endif %}

            <!-- Main Content -->
            <div class="prose-content">
                {{ post.content|safe }}
            </div>
        </div>

        <!-- Tags Section -->
        {% if post.tags.all %}
        <div style="margin: 3rem 0; padding: 2rem; background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border-radius: 20px; border: 1px solid rgba(220, 38, 38, 0.1);">
            <h3 style="font-family: 'Montserrat', sans-serif; font-weight: 700; color: #374151; margin-bottom: 1rem; font-size: 1.25rem;">Related Topics</h3>
            <div style="display: flex; flex-wrap: wrap; gap: 0.75rem;">
                {% for tag in post.tags.all %}
                <span style="display: inline-flex; align-items: center; padding: 0.5rem 1rem; background: rgba(220, 38, 38, 0.1); color: #dc2626; border-radius: 25px; font-size: 0.875rem; font-weight: 600; transition: all 0.3s ease; cursor: pointer;">
                    <i class="fas fa-tag" style="margin-right: 0.5rem; font-size: 0.75rem;"></i>
                    {{ tag.name }}
                </span>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Share Section -->
        <div style="margin: 3rem 0; padding: 2rem; background: linear-gradient(135deg, #1f2937 0%, #374151 100%); border-radius: 20px; text-align: center;">
            <h3 style="font-family: 'Montserrat', sans-serif; font-weight: 700; color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Share This Article</h3>
            <div style="display: flex; justify-content: center; gap: 1rem; flex-wrap: wrap;">
                <a href="https://twitter.com/intent/tweet?text={{ post.title|urlencode }}&url={{ request.build_absolute_uri }}"
                   target="_blank"
                   style="display: flex; align-items: center; justify-content: center; width: 50px; height: 50px; background: linear-gradient(135deg, #1da1f2 0%, #0d8bd9 100%); color: white; border-radius: 50%; text-decoration: none; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(29, 161, 242, 0.3);">
                    <i class="fab fa-twitter" style="font-size: 1.25rem;"></i>
                </a>
                <a href="https://www.facebook.com/sharer/sharer.php?u={{ request.build_absolute_uri }}"
                   target="_blank"
                   style="display: flex; align-items: center; justify-content: center; width: 50px; height: 50px; background: linear-gradient(135deg, #4267B2 0%, #365899 100%); color: white; border-radius: 50%; text-decoration: none; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(66, 103, 178, 0.3);">
                    <i class="fab fa-facebook-f" style="font-size: 1.25rem;"></i>
                </a>
                <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ request.build_absolute_uri }}"
                   target="_blank"
                   style="display: flex; align-items: center; justify-content: center; width: 50px; height: 50px; background: linear-gradient(135deg, #0077b5 0%, #005885 100%); color: white; border-radius: 50%; text-decoration: none; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0, 119, 181, 0.3);">
                    <i class="fab fa-linkedin-in" style="font-size: 1.25rem;"></i>
                </a>
                <a href="https://wa.me/?text={{ post.title|urlencode }}%20{{ request.build_absolute_uri }}"
                   target="_blank"
                   style="display: flex; align-items: center; justify-content: center; width: 50px; height: 50px; background: linear-gradient(135deg, #25d366 0%, #128c7e 100%); color: white; border-radius: 50%; text-decoration: none; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);">
                    <i class="fab fa-whatsapp" style="font-size: 1.25rem;"></i>
                </a>
            </div>
        </div>

        <!-- Navigation Section -->
        <div style="margin: 3rem 0; text-align: center;">
            <a href="{% url 'core:resources' %}"
               style="display: inline-flex; align-items: center; gap: 0.75rem; padding: 1rem 2rem; background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%); color: white; border-radius: 15px; text-decoration: none; font-weight: 600; font-family: 'Montserrat', sans-serif; transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1); box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);">
                <i class="fas fa-arrow-left"></i>
                <span>Back to Resources</span>
            </a>
        </div>
    </section>
</div>
{% endblock %}
