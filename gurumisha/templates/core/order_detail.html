{% extends 'base.html' %}
{% load static %}

{% block title %}Order Details - {{ order.order_number }} - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<!-- Order Detail Page -->
<section class="py-12 bg-gray-50 min-h-screen">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- <PERSON> Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-heading font-bold text-harrier-dark mb-2">Order Details</h1>
            <p class="text-gray-600">Order #{{ order.order_number }}</p>
        </div>

        <!-- Order Status Card -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-semibold text-harrier-dark">Order Status</h2>
                <div class="flex space-x-4">
                    <!-- Order Status Badge -->
                    <span class="px-4 py-2 rounded-full text-sm font-semibold
                        {% if order.status == 'pending' %}bg-yellow-100 text-yellow-800
                        {% elif order.status == 'paid' %}bg-green-100 text-green-800
                        {% elif order.status == 'processing' %}bg-blue-100 text-blue-800
                        {% elif order.status == 'shipped' %}bg-purple-100 text-purple-800
                        {% elif order.status == 'delivered' %}bg-green-100 text-green-800
                        {% elif order.status == 'cancelled' %}bg-red-100 text-red-800
                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ order.get_status_display }}
                    </span>
                    
                    <!-- Payment Status Badge -->
                    <span class="px-4 py-2 rounded-full text-sm font-semibold
                        {% if order.payment_status == 'pending' %}bg-yellow-100 text-yellow-800
                        {% elif order.payment_status == 'processing' %}bg-blue-100 text-blue-800
                        {% elif order.payment_status == 'completed' %}bg-green-100 text-green-800
                        {% elif order.payment_status == 'failed' %}bg-red-100 text-red-800
                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                        Payment: {{ order.get_payment_status_display }}
                    </span>
                </div>
            </div>

            <!-- Order Progress -->
            <div class="relative">
                <div class="flex items-center justify-between">
                    <div class="flex flex-col items-center">
                        <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                            <i class="fas fa-check"></i>
                        </div>
                        <span class="mt-2 text-sm font-medium text-green-600">Ordered</span>
                    </div>
                    
                    <div class="flex flex-col items-center">
                        <div class="w-8 h-8 {% if order.payment_status == 'completed' %}bg-green-500 text-white{% else %}bg-gray-300 text-gray-500{% endif %} rounded-full flex items-center justify-center text-sm font-semibold">
                            {% if order.payment_status == 'completed' %}<i class="fas fa-check"></i>{% else %}2{% endif %}
                        </div>
                        <span class="mt-2 text-sm font-medium {% if order.payment_status == 'completed' %}text-green-600{% else %}text-gray-500{% endif %}">Paid</span>
                    </div>
                    
                    <div class="flex flex-col items-center">
                        <div class="w-8 h-8 {% if order.status in 'processing,shipped,delivered' %}bg-green-500 text-white{% else %}bg-gray-300 text-gray-500{% endif %} rounded-full flex items-center justify-center text-sm font-semibold">
                            {% if order.status in 'processing,shipped,delivered' %}<i class="fas fa-check"></i>{% else %}3{% endif %}
                        </div>
                        <span class="mt-2 text-sm font-medium {% if order.status in 'processing,shipped,delivered' %}text-green-600{% else %}text-gray-500{% endif %}">Processing</span>
                    </div>
                    
                    <div class="flex flex-col items-center">
                        <div class="w-8 h-8 {% if order.status in 'shipped,delivered' %}bg-green-500 text-white{% else %}bg-gray-300 text-gray-500{% endif %} rounded-full flex items-center justify-center text-sm font-semibold">
                            {% if order.status in 'shipped,delivered' %}<i class="fas fa-check"></i>{% else %}4{% endif %}
                        </div>
                        <span class="mt-2 text-sm font-medium {% if order.status in 'shipped,delivered' %}text-green-600{% else %}text-gray-500{% endif %}">Shipped</span>
                    </div>
                    
                    <div class="flex flex-col items-center">
                        <div class="w-8 h-8 {% if order.status == 'delivered' %}bg-green-500 text-white{% else %}bg-gray-300 text-gray-500{% endif %} rounded-full flex items-center justify-center text-sm font-semibold">
                            {% if order.status == 'delivered' %}<i class="fas fa-check"></i>{% else %}5{% endif %}
                        </div>
                        <span class="mt-2 text-sm font-medium {% if order.status == 'delivered' %}text-green-600{% else %}text-gray-500{% endif %}">Delivered</span>
                    </div>
                </div>
                
                <!-- Progress Line -->
                <div class="absolute top-4 left-4 right-4 h-0.5 bg-gray-300 -z-10"></div>
                <div class="absolute top-4 left-4 h-0.5 bg-green-500 -z-10 transition-all duration-500"
                     style="width: {% if order.status == 'delivered' %}100{% elif order.status in 'shipped' %}75{% elif order.status in 'processing' %}50{% elif order.payment_status == 'completed' %}25{% else %}0{% endif %}%;"></div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Order Items -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h3 class="text-lg font-semibold text-harrier-dark mb-4">Order Items</h3>
                <div class="space-y-4">
                    {% for item in order.items.all %}
                        <div class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                            <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                {% if item.spare_part.main_image %}
                                    <img src="{{ item.spare_part.main_image.url }}" 
                                         alt="{{ item.spare_part.name }}" 
                                         class="w-full h-full object-cover rounded-lg">
                                {% else %}
                                    <i class="fas fa-cog text-gray-400 text-xl"></i>
                                {% endif %}
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-harrier-dark">{{ item.part_name }}</h4>
                                <p class="text-sm text-gray-600">SKU: {{ item.part_sku }}</p>
                                <p class="text-sm text-gray-600">Qty: {{ item.quantity }}</p>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-harrier-red">KSh {{ item.total_price|floatformat:0 }}</p>
                                <p class="text-sm text-gray-600">@ KSh {{ item.unit_price|floatformat:0 }}</p>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Order Summary & Customer Info -->
            <div class="space-y-6">
                <!-- Customer Information -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-semibold text-harrier-dark mb-4">Customer Information</h3>
                    <div class="space-y-3">
                        <div>
                            <label class="text-sm font-medium text-gray-600">Name</label>
                            <p class="text-harrier-dark">{{ order.customer_name }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-600">Email</label>
                            <p class="text-harrier-dark">{{ order.customer_email }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-600">Phone</label>
                            <p class="text-harrier-dark">{{ order.customer_phone }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-600">Shipping Address</label>
                            <p class="text-harrier-dark">{{ order.shipping_address }}</p>
                            <p class="text-harrier-dark">{{ order.shipping_city }}</p>
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-semibold text-harrier-dark mb-4">Order Summary</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Subtotal</span>
                            <span class="text-harrier-dark">KSh {{ order.subtotal|floatformat:0 }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Shipping</span>
                            <span class="text-harrier-dark">KSh {{ order.shipping_cost|floatformat:0 }}</span>
                        </div>
                        <div class="border-t pt-3">
                            <div class="flex justify-between font-semibold text-lg">
                                <span class="text-harrier-dark">Total</span>
                                <span class="text-harrier-red">KSh {{ order.total_amount|floatformat:0 }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Information -->
                {% if payments %}
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-semibold text-harrier-dark mb-4">Payment Information</h3>
                    {% for payment in payments %}
                        <div class="border border-gray-200 rounded-lg p-4 {% if not forloop.last %}mb-4{% endif %}">
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-medium text-harrier-dark">{{ payment.payment_method|title }}</span>
                                <span class="px-3 py-1 rounded-full text-sm font-semibold
                                    {% if payment.status == 'completed' %}bg-green-100 text-green-800
                                    {% elif payment.status == 'processing' %}bg-blue-100 text-blue-800
                                    {% elif payment.status == 'failed' %}bg-red-100 text-red-800
                                    {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                    {{ payment.get_status_display }}
                                </span>
                            </div>
                            <div class="text-sm text-gray-600">
                                <p>Amount: KSh {{ payment.amount|floatformat:0 }}</p>
                                <p>Payment ID: {{ payment.payment_id }}</p>
                                {% if payment.mpesa_receipt_number %}
                                    <p>M-Pesa Receipt: {{ payment.mpesa_receipt_number }}</p>
                                {% endif %}
                                <p>Date: {{ payment.created_at|date:"M d, Y H:i" }}</p>
                            </div>
                        </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-8 flex justify-center space-x-4">
            <a href="{% url 'core:spare_parts' %}" 
               class="px-6 py-3 bg-harrier-red text-white rounded-lg hover:bg-red-700 transition-colors">
                Continue Shopping
            </a>
            <a href="{% url 'core:dashboard' %}" 
               class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                View All Orders
            </a>
        </div>
    </div>
</section>

{% if order.payment_status == 'completed' %}
<!-- Success Message -->
<div class="fixed bottom-4 right-4 bg-green-500 text-white px-6 py-4 rounded-lg shadow-lg animate-bounce">
    <div class="flex items-center">
        <i class="fas fa-check-circle mr-2"></i>
        <span>Payment Successful!</span>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh page if payment is still processing
    {% if order.payment_status == 'processing' %}
    setTimeout(function() {
        location.reload();
    }, 10000); // Refresh every 10 seconds
    {% endif %}
</script>
{% endblock %}
