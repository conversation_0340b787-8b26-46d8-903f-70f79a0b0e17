<!-- HTMX Partial Template for Admin Spare Shop Table -->
<table class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gradient-to-r from-gray-50 to-gray-100">
        <tr>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                <div class="flex items-center">
                    <i class="fas fa-cog mr-2 text-amber-600"></i>
                    Part Details
                </div>
            </th>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                <div class="flex items-center">
                    <i class="fas fa-tags mr-2 text-blue-600"></i>
                    Category
                </div>
            </th>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                <div class="flex items-center">
                    <i class="fas fa-boxes mr-2 text-green-600"></i>
                    Stock Status
                </div>
            </th>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                <div class="flex items-center">
                    <i class="fas fa-dollar-sign mr-2 text-emerald-600"></i>
                    Price
                </div>
            </th>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                <div class="flex items-center">
                    <i class="fas fa-truck mr-2 text-purple-600"></i>
                    Supplier
                </div>
            </th>
            <th class="px-6 py-4 text-center text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                <div class="flex items-center justify-center">
                    <i class="fas fa-tools mr-2 text-harrier-red"></i>
                    Actions
                </div>
            </th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% if spare_parts %}
            {% for part in spare_parts %}
            <tr class="hover:bg-gray-50 transition-colors duration-200">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        {% if part.main_image %}
                            <img src="{{ part.main_image.url }}" alt="{{ part.name }}" class="w-12 h-12 object-cover rounded-lg border-2 border-gray-200 mr-4">
                        {% else %}
                            <div class="w-12 h-12 bg-gradient-to-br from-amber-200 to-amber-300 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-cogs text-amber-600 text-lg"></i>
                            </div>
                        {% endif %}
                        <div>
                            <div class="text-sm font-medium text-harrier-dark font-raleway">{{ part.name }}</div>
                            <div class="text-sm text-gray-500">{{ part.part_number }}</div>
                            {% if part.sku %}
                            <div class="text-xs text-gray-600">
                                <i class="fas fa-barcode mr-1"></i>{{ part.sku }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 font-montserrat">
                        {{ part.category_new.name|default:part.category|default:"Uncategorized" }}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        {% if part.stock_quantity > 10 %}
                            <div class="w-3 h-3 bg-green-400 rounded-full mr-2"></div>
                            <span class="text-sm text-green-800 font-medium">In Stock</span>
                        {% elif part.stock_quantity > 0 %}
                            <div class="w-3 h-3 bg-orange-400 rounded-full mr-2"></div>
                            <span class="text-sm text-orange-800 font-medium">Low Stock</span>
                        {% else %}
                            <div class="w-3 h-3 bg-red-400 rounded-full mr-2"></div>
                            <span class="text-sm text-red-800 font-medium">Out of Stock</span>
                        {% endif %}
                    </div>
                    <div class="text-xs text-gray-500 mt-1">{{ part.stock_quantity }} units</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-semibold text-harrier-dark">KSh {{ part.price|floatformat:0 }}</div>
                    {% if part.cost_price %}
                    <div class="text-xs text-gray-500">Cost: KSh {{ part.cost_price|floatformat:0 }}</div>
                    {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    {% if part.supplier %}
                    <div class="text-sm text-gray-900 font-raleway">{{ part.supplier.name }}</div>
                    <div class="text-xs text-gray-500">{{ part.supplier.contact_email }}</div>
                    {% else %}
                    <span class="text-sm text-gray-500">No supplier</span>
                    {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex flex-wrap gap-2 justify-center">
                        <button onclick="openEditPartModal({{ part.id }})" 
                                class="pill-action-btn pill-btn-edit">
                            <i class="fas fa-edit mr-1"></i>Edit
                        </button>
                        <button onclick="openRestockModal({{ part.id }})" 
                                class="pill-action-btn pill-btn-restock">
                            <i class="fas fa-plus-circle mr-1"></i>Restock
                        </button>
                        <button onclick="openViewPartModal({{ part.id }})" 
                                class="pill-action-btn pill-btn-view">
                            <i class="fas fa-eye mr-1"></i>View
                        </button>
                        <button onclick="deleteSparePartConfirm({{ part.id }})" 
                                class="pill-action-btn pill-btn-delete">
                            <i class="fas fa-trash mr-1"></i>Delete
                        </button>
                    </div>
                </td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="6" class="px-6 py-12 text-center">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-cogs text-gray-400 text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2 font-montserrat">No spare parts found</h4>
                    <p class="text-gray-600 font-raleway">
                        {% if search_query %}
                            No parts match your search criteria. Try adjusting your filters.
                        {% else %}
                            Add spare parts to your inventory to get started.
                        {% endif %}
                    </p>
                    {% if not search_query %}
                    <button onclick="openAddPartModal()" class="mt-4 enhanced-btn enhanced-btn-primary">
                        <i class="fas fa-plus mr-2"></i>Add First Spare Part
                    </button>
                    {% endif %}
                </td>
            </tr>
        {% endif %}
    </tbody>
</table>

<!-- Pagination -->
{% if page_obj.has_other_pages %}
<div class="flex items-center justify-between px-6 py-4 bg-gray-50 border-t border-gray-200">
    <div class="flex items-center text-sm text-gray-700">
        <span>Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results</span>
    </div>
    <div class="flex items-center space-x-2">
        {% if page_obj.has_previous %}
            <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_availability %}&availability={{ current_availability }}{% endif %}{% if current_brand %}&brand={{ current_brand }}{% endif %}{% if current_price_range %}&price_range={{ current_price_range }}{% endif %}{% if current_condition %}&condition={{ current_condition }}{% endif %}"
               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                Previous
            </a>
        {% endif %}

        {% for num in page_obj.paginator.page_range %}
            {% if page_obj.number == num %}
                <span class="px-3 py-2 text-sm font-medium text-white bg-harrier-red border border-harrier-red rounded-md">
                    {{ num }}
                </span>
            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                <a href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_availability %}&availability={{ current_availability }}{% endif %}{% if current_brand %}&brand={{ current_brand }}{% endif %}{% if current_price_range %}&price_range={{ current_price_range }}{% endif %}{% if current_condition %}&condition={{ current_condition }}{% endif %}"
                   class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                    {{ num }}
                </a>
            {% endif %}
        {% endfor %}

        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_availability %}&availability={{ current_availability }}{% endif %}{% if current_brand %}&brand={{ current_brand }}{% endif %}{% if current_price_range %}&price_range={{ current_price_range }}{% endif %}{% if current_condition %}&condition={{ current_condition }}{% endif %}"
               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                Next
            </a>
        {% endif %}
    </div>
</div>
{% endif %}
