{% extends 'base.html' %}
{% load static %}

{% block title %}Sell Your Car - Gurumisha{% endblock %}

{% block extra_css %}
{{ block.super }}
<link rel="stylesheet" href="{% static 'css/enhanced-profile.css' %}">
<link rel="stylesheet" href="{% static 'css/dashboard-mobile.css' %}">
<style>
/* Enhanced Sell Car Page Styles */
:root {
    --harrier-red: #DC2626;
    --harrier-red-light: #EF4444;
    --harrier-red-dark: #B91C1C;
    --harrier-dark: #1F2937;
    --harrier-blue: #1E3A8A;
    --harrier-blue-light: #3B82F6;
    --harrier-white: #FFFFFF;
    --harrier-gray: #F9FAFB;
    --harrier-gray-dark: #6B7280;

    --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
    --ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);
    --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);

    --duration-fast: 150ms;
    --duration-normal: 250ms;
    --duration-slow: 350ms;
}

/* Enhanced Hero Section */
.sell-car-hero {
    position: relative;
    min-height: 70vh;
    background: linear-gradient(135deg, var(--harrier-red) 0%, var(--harrier-dark) 50%, var(--harrier-blue) 100%);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sell-car-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('{% static "images/car-image-1-1200x600.jpg" %}');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0.15;
    z-index: 1;
}

.sell-car-hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.3;
    z-index: 2;
}

.sell-car-hero-content {
    position: relative;
    z-index: 3;
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 2rem;
}

.sell-car-hero-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f3f4f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: fadeInUp 0.8s var(--ease-out-quart);
}

.sell-car-hero-subtitle {
    font-family: 'Raleway', sans-serif;
    font-size: 1.25rem;
    font-weight: 400;
    line-height: 1.6;
    margin-bottom: 2rem;
    opacity: 0.9;
    animation: fadeInUp 0.8s var(--ease-out-quart) 0.2s both;
}

.sell-car-hero-features {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-top: 2rem;
    animation: fadeInUp 0.8s var(--ease-out-quart) 0.4s both;
}

.sell-car-hero-feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-family: 'Raleway', sans-serif;
    font-weight: 500;
    opacity: 0.9;
}

.sell-car-hero-feature i {
    color: var(--harrier-red-light);
    font-size: 1.1rem;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animation Classes */
.animate-fade-in-up {
    animation: fadeInUp 0.8s var(--ease-out-quart) both;
    /* Fallback to ensure visibility */
    opacity: 1;
    transform: translateY(0);
}

/* Responsive Hero */
/* Enhanced Form Styling */
.enhanced-sell-car-form .form-section {
    transition: all var(--duration-normal) var(--ease-out-quart);
}

.enhanced-sell-car-form .form-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.enhanced-label {
    transition: color var(--duration-fast) var(--ease-out-quart);
}

.enhanced-input,
.enhanced-select,
.enhanced-textarea {
    transition: all var(--duration-normal) var(--ease-out-quart);
    position: relative;
}

.enhanced-input:focus,
.enhanced-select:focus,
.enhanced-textarea:focus {
    transform: translateY(-1px);
    box-shadow: 0 10px 25px -5px rgba(220, 38, 38, 0.2), 0 4px 6px -2px rgba(220, 38, 38, 0.1);
}

.enhanced-file-input {
    transition: all var(--duration-normal) var(--ease-out-quart);
}

.enhanced-checkbox {
    transition: all var(--duration-normal) var(--ease-out-quart);
}

.enhanced-checkbox:checked {
    background-color: var(--harrier-red);
    border-color: var(--harrier-red);
}

.enhanced-submit-btn:hover {
    background: linear-gradient(135deg, var(--harrier-red-light) 0%, var(--harrier-red-dark) 100%);
}

.enhanced-cancel-btn:hover {
    background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);
}

.glassmorphism-card {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    /* Ensure visibility */
    opacity: 1 !important;
    visibility: visible !important;
}

/* Form sections visibility */
.form-section {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    /* Fallback background for browsers without backdrop-filter support */
    background: rgba(255, 255, 255, 0.9) !important;
}

/* Ensure form is always visible */
.enhanced-sell-car-form {
    opacity: 1 !important;
    visibility: visible !important;
}

.section-header {
    position: relative;
}

.section-header::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, var(--harrier-red) 0%, var(--harrier-blue) 100%);
    border-radius: 2px;
}

.error-message {
    animation: shake 0.5s var(--ease-out-quart);
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.help-text {
    opacity: 0.8;
    transition: opacity var(--duration-fast) var(--ease-out-quart);
}

.form-group:hover .help-text {
    opacity: 1;
}

.upload-guidelines {
    transition: all var(--duration-normal) var(--ease-out-quart);
}

.negotiable-option {
    transition: all var(--duration-normal) var(--ease-out-quart);
}

.negotiable-option:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.review-notice {
    animation: fadeInUp 0.6s var(--ease-out-quart) 0.8s both;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sell-car-hero {
        min-height: 60vh;
    }

    .sell-car-hero-title {
        font-size: 2.5rem;
    }

    .sell-car-hero-subtitle {
        font-size: 1.1rem;
    }

    .sell-car-hero-features {
        gap: 1rem;
    }

    .form-section {
        padding: 1.5rem !important;
    }

    .enhanced-submit-btn,
    .enhanced-cancel-btn {
        padding: 1rem 1.5rem !important;
        font-size: 1rem !important;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Enhanced Hero Section -->
<section class="sell-car-hero">
    <div class="sell-car-hero-content">
        <h1 class="sell-car-hero-title">
            Sell Your Car with Confidence
        </h1>
        <p class="sell-car-hero-subtitle">
            Join thousands of satisfied sellers on Kenya's most trusted automotive marketplace.
            Get the best value for your vehicle with our premium listing service.
        </p>
        <div class="sell-car-hero-features">
            <div class="sell-car-hero-feature">
                <i class="fas fa-shield-alt"></i>
                <span>Verified Buyers</span>
            </div>
            <div class="sell-car-hero-feature">
                <i class="fas fa-clock"></i>
                <span>Quick Approval</span>
            </div>
            <div class="sell-car-hero-feature">
                <i class="fas fa-handshake"></i>
                <span>Secure Transactions</span>
            </div>
            <div class="sell-car-hero-feature">
                <i class="fas fa-star"></i>
                <span>Premium Exposure</span>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Sell Car Form Section -->
<section class="py-20 bg-gradient-to-br from-harrier-gray via-white to-harrier-gray relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\'40\' height=\'40\' viewBox=\'0 0 40 40\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'%23DC2626\' fill-opacity=\'0.1\'%3E%3Cpath d=\'M20 20c0 11.046-8.954 20-20 20s-20-8.954-20-20 8.954-20 20-20 20 8.954 20 20zm-20-16c-8.837 0-16 7.163-16 16s7.163 16 16 16 16-7.163 16-16-7.163-16-16-16z\'/%3E%3C/g%3E%3C/svg%3E');"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <div class="max-w-5xl mx-auto">
            <!-- Enhanced Progress Steps -->
            <div class="mb-16 animate-fade-in-up">
                <div class="flex items-center justify-center space-x-6 md:space-x-12">
                    <div class="flex items-center group">
                        <div class="w-12 h-12 bg-gradient-to-r from-harrier-red to-harrier-red-light text-white rounded-full flex items-center justify-center text-lg font-bold shadow-lg transform transition-all duration-300 group-hover:scale-110">
                            <i class="fas fa-car"></i>
                        </div>
                        <span class="ml-3 text-base font-semibold text-harrier-dark font-montserrat">Car Details</span>
                    </div>
                    <div class="w-16 h-1 bg-gradient-to-r from-harrier-red to-gray-300 rounded-full"></div>
                    <div class="flex items-center group">
                        <div class="w-12 h-12 bg-gray-200 text-gray-500 rounded-full flex items-center justify-center text-lg font-bold shadow-md transition-all duration-300 group-hover:bg-gray-300">
                            <i class="fas fa-eye"></i>
                        </div>
                        <span class="ml-3 text-base font-medium text-gray-500 font-montserrat">Review</span>
                    </div>
                    <div class="w-16 h-1 bg-gray-300 rounded-full"></div>
                    <div class="flex items-center group">
                        <div class="w-12 h-12 bg-gray-200 text-gray-500 rounded-full flex items-center justify-center text-lg font-bold shadow-md transition-all duration-300 group-hover:bg-gray-300">
                            <i class="fas fa-check"></i>
                        </div>
                        <span class="ml-3 text-base font-medium text-gray-500 font-montserrat">Published</span>
                    </div>
                </div>
            </div>

            <!-- Enhanced Form Container with Glassmorphism -->
            <div class="glassmorphism-card bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl p-8 lg:p-16 border border-white/20">
                <!-- Visibility Check -->
                <div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-800 rounded-lg text-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <strong>Form is loading correctly!</strong> If you can see this message, the form container is visible.
                </div>

                <div class="mb-12 text-center">
                    <h2 class="text-4xl lg:text-5xl font-montserrat font-bold text-harrier-dark mb-4 bg-gradient-to-r from-harrier-dark via-harrier-blue to-harrier-red bg-clip-text text-transparent">
                        List Your Vehicle
                    </h2>
                    <p class="text-lg text-harrier-gray-dark font-raleway leading-relaxed max-w-2xl mx-auto">
                        Provide detailed information about your car to attract serious buyers and get the best value for your vehicle
                    </p>
                    <div class="mt-6 flex justify-center">
                        <div class="w-24 h-1 bg-gradient-to-r from-harrier-red to-harrier-blue rounded-full"></div>
                    </div>
                </div>

                <!-- Enhanced Messages Display -->
                {% if messages %}
                    <div class="mb-8 space-y-4">
                        {% for message in messages %}
                            <div class="enhanced-message p-6 rounded-2xl border-l-4 {% if message.tags == 'error' %}bg-red-50/80 border-red-500 text-red-800{% elif message.tags == 'success' %}bg-green-50/80 border-green-500 text-green-800{% else %}bg-blue-50/80 border-blue-500 text-blue-800{% endif %} backdrop-blur-sm font-raleway animate-fade-in-up">
                                <div class="flex items-center">
                                    <i class="{% if message.tags == 'error' %}fas fa-exclamation-triangle text-red-500{% elif message.tags == 'success' %}fas fa-check-circle text-green-500{% else %}fas fa-info-circle text-blue-500{% endif %} mr-3 text-lg"></i>
                                    <span class="font-medium">{{ message }}</span>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}

                <form method="post" enctype="multipart/form-data" class="enhanced-sell-car-form space-y-12" hx-boost="true">
                    {% csrf_token %}

                    <!-- Enhanced Basic Information Section -->
                    <div class="form-section bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-white/30 shadow-lg">
                        <div class="section-header mb-8">
                            <h3 class="text-2xl font-montserrat font-bold text-harrier-dark mb-2 flex items-center">
                                <i class="fas fa-info-circle text-harrier-red mr-3"></i>
                                Basic Information
                            </h3>
                            <p class="text-harrier-gray-dark font-raleway">Essential details about your vehicle listing</p>
                        </div>
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <div class="form-group">
                                <label for="{{ form.title.id_for_label }}" class="enhanced-label block text-base font-semibold text-harrier-dark mb-3 font-montserrat">
                                    <i class="fas fa-tag text-harrier-red mr-2"></i>
                                    Listing Title <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.title }}
                                {% if form.title.errors %}
                                    <div class="error-message mt-2 text-sm text-red-600 font-raleway flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.title.errors.0 }}
                                    </div>
                                {% endif %}
                                <p class="help-text mt-2 text-sm text-harrier-gray-dark font-raleway">Create an attractive title that highlights your car's best features</p>
                            </div>
                            <div class="form-group">
                                <label for="{{ form.price.id_for_label }}" class="enhanced-label block text-base font-semibold text-harrier-dark mb-3 font-montserrat">
                                    <i class="fas fa-money-bill-wave text-harrier-red mr-2"></i>
                                    Price (KES) <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.price }}
                                {% if form.price.errors %}
                                    <div class="error-message mt-2 text-sm text-red-600 font-raleway flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.price.errors.0 }}
                                    </div>
                                {% endif %}
                                <p class="help-text mt-2 text-sm text-harrier-gray-dark font-raleway">Set a competitive price based on market value</p>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Vehicle Details Section -->
                    <div class="form-section bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-white/30 shadow-lg">
                        <div class="section-header mb-8">
                            <h3 class="text-2xl font-montserrat font-bold text-harrier-dark mb-2 flex items-center">
                                <i class="fas fa-car text-harrier-red mr-3"></i>
                                Vehicle Details
                            </h3>
                            <p class="text-harrier-gray-dark font-raleway">Specify your vehicle's make, model, and key characteristics</p>
                        </div>

                        <!-- Primary Vehicle Info -->
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                            <div class="form-group">
                                <label for="{{ form.brand.id_for_label }}" class="enhanced-label block text-base font-semibold text-harrier-dark mb-3 font-montserrat">
                                    <i class="fas fa-industry text-harrier-red mr-2"></i>
                                    Brand <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.brand }}
                                {% if form.brand.errors %}
                                    <div class="error-message mt-2 text-sm text-red-600 font-raleway flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.brand.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="form-group">
                                <label for="{{ form.model.id_for_label }}" class="enhanced-label block text-base font-semibold text-harrier-dark mb-3 font-montserrat">
                                    <i class="fas fa-car-side text-harrier-red mr-2"></i>
                                    Model <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.model }}
                                {% if form.model.errors %}
                                    <div class="error-message mt-2 text-sm text-red-600 font-raleway flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.model.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="form-group">
                                <label for="{{ form.year.id_for_label }}" class="enhanced-label block text-base font-semibold text-harrier-dark mb-3 font-montserrat">
                                    <i class="fas fa-calendar-alt text-harrier-red mr-2"></i>
                                    Year <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.year }}
                                {% if form.year.errors %}
                                    <div class="error-message mt-2 text-sm text-red-600 font-raleway flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.year.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Secondary Vehicle Info -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <div class="form-group">
                                <label for="{{ form.condition.id_for_label }}" class="enhanced-label block text-base font-semibold text-harrier-dark mb-3 font-montserrat">
                                    <i class="fas fa-star text-harrier-red mr-2"></i>
                                    Condition <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.condition }}
                                {% if form.condition.errors %}
                                    <div class="error-message mt-2 text-sm text-red-600 font-raleway flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.condition.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="form-group">
                                <label for="{{ form.color.id_for_label }}" class="enhanced-label block text-base font-semibold text-harrier-dark mb-3 font-montserrat">
                                    <i class="fas fa-palette text-harrier-red mr-2"></i>
                                    Color <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.color }}
                                {% if form.color.errors %}
                                    <div class="error-message mt-2 text-sm text-red-600 font-raleway flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.color.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Technical Specifications Section -->
                    <div class="form-section bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-white/30 shadow-lg">
                        <div class="section-header mb-8">
                            <h3 class="text-2xl font-montserrat font-bold text-harrier-dark mb-2 flex items-center">
                                <i class="fas fa-cogs text-harrier-red mr-3"></i>
                                Technical Specifications
                            </h3>
                            <p class="text-harrier-gray-dark font-raleway">Engine, transmission, and performance details</p>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                            <div class="form-group">
                                <label for="{{ form.engine_size.id_for_label }}" class="enhanced-label block text-base font-semibold text-harrier-dark mb-3 font-montserrat">
                                    <i class="fas fa-tachometer-alt text-harrier-red mr-2"></i>
                                    Engine Size
                                </label>
                                {{ form.engine_size }}
                                {% if form.engine_size.errors %}
                                    <div class="error-message mt-2 text-sm text-red-600 font-raleway flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.engine_size.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="form-group">
                                <label for="{{ form.fuel_type.id_for_label }}" class="enhanced-label block text-base font-semibold text-harrier-dark mb-3 font-montserrat">
                                    <i class="fas fa-gas-pump text-harrier-red mr-2"></i>
                                    Fuel Type <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.fuel_type }}
                                {% if form.fuel_type.errors %}
                                    <div class="error-message mt-2 text-sm text-red-600 font-raleway flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.fuel_type.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="form-group">
                                <label for="{{ form.transmission.id_for_label }}" class="enhanced-label block text-base font-semibold text-harrier-dark mb-3 font-montserrat">
                                    <i class="fas fa-cog text-harrier-red mr-2"></i>
                                    Transmission <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.transmission }}
                                {% if form.transmission.errors %}
                                    <div class="error-message mt-2 text-sm text-red-600 font-raleway flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.transmission.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="form-group">
                                <label for="{{ form.mileage.id_for_label }}" class="enhanced-label block text-base font-semibold text-harrier-dark mb-3 font-montserrat">
                                    <i class="fas fa-road text-harrier-red mr-2"></i>
                                    Mileage (KM) <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.mileage }}
                                {% if form.mileage.errors %}
                                    <div class="error-message mt-2 text-sm text-red-600 font-raleway flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.mileage.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Description and Features Section -->
                    <div class="form-section bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-white/30 shadow-lg">
                        <div class="section-header mb-8">
                            <h3 class="text-2xl font-montserrat font-bold text-harrier-dark mb-2 flex items-center">
                                <i class="fas fa-file-alt text-harrier-red mr-3"></i>
                                Description & Features
                            </h3>
                            <p class="text-harrier-gray-dark font-raleway">Detailed description and key features of your vehicle</p>
                        </div>
                        <div class="space-y-8">
                            <div class="form-group">
                                <label for="{{ form.description.id_for_label }}" class="enhanced-label block text-base font-semibold text-harrier-dark mb-3 font-montserrat">
                                    <i class="fas fa-align-left text-harrier-red mr-2"></i>
                                    Description <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.description }}
                                {% if form.description.errors %}
                                    <div class="error-message mt-2 text-sm text-red-600 font-raleway flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.description.errors.0 }}
                                    </div>
                                {% endif %}
                                <p class="help-text mt-2 text-sm text-harrier-gray-dark font-raleway">Provide a detailed description highlighting your car's condition, history, and unique selling points</p>
                            </div>
                            <div class="form-group">
                                <label for="{{ form.features.id_for_label }}" class="enhanced-label block text-base font-semibold text-harrier-dark mb-3 font-montserrat">
                                    <i class="fas fa-list-ul text-harrier-red mr-2"></i>
                                    Features
                                </label>
                                {{ form.features }}
                                <p class="help-text mt-2 text-sm text-harrier-gray-dark font-raleway">
                                    List key features separated by commas (e.g., Air Conditioning, Power Steering, ABS, Airbags, Leather Seats, Navigation System)
                                </p>
                                {% if form.features.errors %}
                                    <div class="error-message mt-2 text-sm text-red-600 font-raleway flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.features.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Image Upload Section -->
                    <div class="form-section bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-white/30 shadow-lg">
                        <div class="section-header mb-8">
                            <h3 class="text-2xl font-montserrat font-bold text-harrier-dark mb-2 flex items-center">
                                <i class="fas fa-camera text-harrier-red mr-3"></i>
                                Main Photo
                            </h3>
                            <p class="text-harrier-gray-dark font-raleway">Upload a high-quality image that showcases your vehicle</p>
                        </div>
                        <div class="form-group">
                            <label for="{{ form.main_image.id_for_label }}" class="enhanced-label block text-base font-semibold text-harrier-dark mb-3 font-montserrat">
                                <i class="fas fa-upload text-harrier-red mr-2"></i>
                                Upload Main Photo <span class="text-harrier-red">*</span>
                            </label>
                            <div class="image-upload-container relative">
                                {{ form.main_image }}
                                <div class="upload-guidelines mt-4 p-4 bg-blue-50/80 rounded-xl border border-blue-200">
                                    <h4 class="font-semibold text-blue-800 mb-2 font-montserrat">Photo Guidelines:</h4>
                                    <ul class="text-sm text-blue-700 space-y-1 font-raleway">
                                        <li>• Use high-resolution images (minimum 800x600px)</li>
                                        <li>• Show the car from the front-side angle</li>
                                        <li>• Ensure good lighting and clear visibility</li>
                                        <li>• Avoid filters or heavy editing</li>
                                    </ul>
                                </div>
                            </div>
                            {% if form.main_image.errors %}
                                <div class="error-message mt-2 text-sm text-red-600 font-raleway flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ form.main_image.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Enhanced Listing Options Section -->
                    <div class="form-section bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-white/30 shadow-lg">
                        <div class="section-header mb-8">
                            <h3 class="text-2xl font-montserrat font-bold text-harrier-dark mb-2 flex items-center">
                                <i class="fas fa-sliders-h text-harrier-red mr-3"></i>
                                Listing Options
                            </h3>
                            <p class="text-harrier-gray-dark font-raleway">Configure how your vehicle will be listed and displayed</p>
                        </div>
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <div class="form-group">
                                <label for="{{ form.listing_type.id_for_label }}" class="enhanced-label block text-base font-semibold text-harrier-dark mb-3 font-montserrat">
                                    <i class="fas fa-tags text-harrier-red mr-2"></i>
                                    Listing Type <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.listing_type }}
                                <p class="help-text mt-2 text-sm text-harrier-gray-dark font-raleway">
                                    Choose how you want to list your car on our platform
                                </p>
                                {% if form.listing_type.errors %}
                                    <div class="error-message mt-2 text-sm text-red-600 font-raleway flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.listing_type.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="form-group">
                                <div class="negotiable-option bg-gradient-to-r from-harrier-gray to-white p-6 rounded-2xl border border-gray-200">
                                    <div class="flex items-start space-x-4">
                                        <div class="flex items-center h-6 mt-1">
                                            {{ form.negotiable }}
                                        </div>
                                        <div class="flex-1">
                                            <label for="{{ form.negotiable.id_for_label }}" class="enhanced-label text-base font-semibold text-harrier-dark font-montserrat cursor-pointer">
                                                <i class="fas fa-handshake text-harrier-red mr-2"></i>
                                                Price is negotiable
                                            </label>
                                            <p class="help-text mt-1 text-sm text-harrier-gray-dark font-raleway">
                                                Check this if you're open to price negotiations with potential buyers
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Submit Section -->
                    <div class="submit-section pt-12 mt-8 border-t-2 border-gradient-to-r from-harrier-red to-harrier-blue">
                        <div class="text-center mb-8">
                            <h4 class="text-xl font-montserrat font-bold text-harrier-dark mb-2">Ready to List Your Vehicle?</h4>
                            <p class="text-harrier-gray-dark font-raleway">Review your information and submit for approval</p>
                        </div>
                        <div class="flex flex-col lg:flex-row gap-6 max-w-2xl mx-auto">
                            <button type="submit" class="enhanced-submit-btn flex-1 bg-gradient-to-r from-harrier-red to-harrier-red-light text-white py-5 px-8 rounded-2xl text-lg font-bold font-montserrat shadow-xl hover:shadow-2xl transform transition-all duration-300 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-harrier-red/30">
                                <i class="fas fa-rocket mr-3"></i>SUBMIT FOR REVIEW
                            </button>
                            <a href="{% url 'core:dashboard' %}" class="enhanced-cancel-btn flex-1 bg-gradient-to-r from-gray-500 to-gray-600 text-white py-5 px-8 rounded-2xl text-lg font-bold font-montserrat shadow-lg hover:shadow-xl transform transition-all duration-300 hover:scale-105 text-center">
                                <i class="fas fa-arrow-left mr-3"></i>CANCEL
                            </a>
                        </div>
                        <div class="review-notice mt-8 p-6 bg-blue-50/80 rounded-2xl border border-blue-200 text-center">
                            <div class="flex items-center justify-center mb-3">
                                <i class="fas fa-info-circle text-blue-600 text-xl mr-2"></i>
                                <h5 class="font-semibold text-blue-800 font-montserrat">Review Process</h5>
                            </div>
                            <p class="text-sm text-blue-700 font-raleway leading-relaxed">
                                Your listing will be carefully reviewed by our team to ensure quality and accuracy.
                                This process typically takes 24-48 hours. You'll receive an email notification once your listing is approved and published.
                            </p>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Why Sell With Us Section -->
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-heading font-bold text-harrier-dark mb-4">Why Sell With Gurumisha Motors?</h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                Join thousands of satisfied sellers who have successfully sold their cars through our platform
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-harrier-red bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-users text-harrier-red text-2xl"></i>
                </div>
                <h3 class="text-xl font-heading font-bold text-harrier-dark mb-2">Large Audience</h3>
                <p class="text-gray-600">Reach thousands of potential buyers actively looking for cars</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-harrier-red bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-shield-alt text-harrier-red text-2xl"></i>
                </div>
                <h3 class="text-xl font-heading font-bold text-harrier-dark mb-2">Secure Platform</h3>
                <p class="text-gray-600">All buyers are verified and transactions are secure</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-harrier-red bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-headset text-harrier-red text-2xl"></i>
                </div>
                <h3 class="text-xl font-heading font-bold text-harrier-dark mb-2">Expert Support</h3>
                <p class="text-gray-600">Our team helps you throughout the selling process</p>
            </div>
        </div>
    </div>
</section>
{% endblock %}


