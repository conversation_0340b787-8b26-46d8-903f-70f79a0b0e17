<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile JavaScript Test</title>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .test-button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .stat-card { padding: 10px; margin: 5px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; cursor: pointer; }
        .contact-item { padding: 10px; margin: 5px; background: #e9ecef; border: 1px solid #ced4da; border-radius: 5px; cursor: pointer; }
        .nav-tab { padding: 10px 15px; margin: 5px; background: #6c757d; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .nav-tab.active { background: #dc2626; }
        .tab-content-panel { padding: 20px; margin: 10px 0; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; display: none; }
        .tab-content-panel.active { display: block; }
        #toast-container { position: fixed; top: 20px; right: 20px; z-index: 1000; }
        .toast-notification { padding: 10px 15px; margin: 5px 0; border-radius: 5px; color: white; }
    </style>
</head>
<body>
    <h1>Profile JavaScript Functionality Test</h1>
    
    <!-- Toast Container -->
    <div id="toast-container"></div>
    
    <!-- Basic Function Tests -->
    <div class="test-section">
        <h2>Basic Function Tests</h2>
        <button class="test-button" onclick="testClickFunctionality()">Test Toast System</button>
        <button class="test-button" onclick="simpleShowToast('Test message', 'success')">Show Success Toast</button>
        <button class="test-button" onclick="simpleShowToast('Error message', 'error')">Show Error Toast</button>
        <button class="test-button" onclick="simpleShowToast('Warning message', 'warning')">Show Warning Toast</button>
    </div>
    
    <!-- Stat Card Tests -->
    <div class="test-section">
        <h2>Stat Card Tests</h2>
        <div class="stat-card" onclick="handleStatClick('complete')">Profile Completion</div>
        <div class="stat-card" onclick="handleStatClick('listings')">My Listings</div>
        <div class="stat-card" onclick="handleStatClick('orders')">My Orders</div>
        <div class="stat-card" onclick="handleStatClick('member since')">Member Since</div>
    </div>
    
    <!-- Contact Item Tests -->
    <div class="test-section">
        <h2>Contact Item Tests</h2>
        <div class="contact-item" onclick="handleContactClick('email', '<EMAIL>')">Email: <EMAIL></div>
        <div class="contact-item" onclick="handleContactClick('phone', '+1234567890')">Phone: +1234567890</div>
        <div class="contact-item" onclick="handleContactClick('location', 'New York, USA')">Location: New York, USA</div>
    </div>
    
    <!-- Tab Navigation Tests -->
    <div class="test-section">
        <h2>Tab Navigation Tests</h2>
        <div>
            <button class="nav-tab active" data-tab="personal" onclick="switchToTab('personal')">Personal</button>
            <button class="nav-tab" data-tab="contact" onclick="switchToTab('contact')">Contact</button>
            <button class="nav-tab" data-tab="preferences" onclick="switchToTab('preferences')">Preferences</button>
        </div>
        
        <div class="tab-content-panel active" id="personal-tab">
            <h3>Personal Information</h3>
            <p>This is the personal information tab content.</p>
        </div>
        
        <div class="tab-content-panel" id="contact-tab">
            <h3>Contact Information</h3>
            <p>This is the contact information tab content.</p>
        </div>
        
        <div class="tab-content-panel" id="preferences-tab">
            <h3>Preferences</h3>
            <p>This is the preferences tab content.</p>
        </div>
    </div>
    
    <!-- Form Tests -->
    <div class="test-section">
        <h2>Form Tests</h2>
        <form id="testForm">
            <input type="text" id="test_field" placeholder="Test field">
            <input type="password" id="test_password" placeholder="Password">
            <button type="button" onclick="togglePassword('test_password')">Toggle Password</button>
            <button type="button" onclick="resetForm()">Reset Form</button>
        </form>
    </div>
    
    <!-- Image Tests -->
    <div class="test-section">
        <h2>Image Function Tests</h2>
        <button class="test-button" onclick="removeProfilePicture()">Remove Profile Picture</button>
        <button class="test-button" onclick="openImageCropper()">Open Image Cropper</button>
        <button class="test-button" onclick="setAspectRatio(1)">Set Square Aspect</button>
    </div>
    
    <!-- Console Output -->
    <div class="test-section">
        <h2>Console Output</h2>
        <p>Check the browser console for detailed logs of function calls and any errors.</p>
        <button class="test-button" onclick="console.log('Available functions:', Object.keys(window).filter(key => typeof window[key] === 'function' && key.startsWith('handle') || key.startsWith('switch') || key.startsWith('simple')))">List Available Functions</button>
    </div>

    <script>
        // Include the profile JavaScript functions here
        // Global variables
        window.showToast = null;
        window.handleStatClick = null;
        window.handleContactClick = null;
        window.handleBadgeClick = null;

        // Simple toast system
        function simpleShowToast(message, type) {
            console.log('Toast:', message, type);

            let container = document.getElementById('toast-container');
            if (!container) {
                container = document.createElement('div');
                container.id = 'toast-container';
                container.className = 'fixed top-4 right-4 z-50 space-y-2';
                document.body.appendChild(container);
            }

            const toast = document.createElement('div');
            let bgColor = 'bg-blue-500';
            if (type === 'success') bgColor = 'bg-green-500';
            if (type === 'error') bgColor = 'bg-red-500';
            if (type === 'warning') bgColor = 'bg-yellow-500';

            toast.className = `${bgColor} text-white p-4 rounded-lg shadow-lg mb-2`;
            toast.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200 text-xl">&times;</button>
                </div>
            `;

            container.appendChild(toast);
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 5000);
        }

        // Click handler functions
        function handleStatClick(type) {
            console.log('Stat clicked:', type);
            simpleShowToast(`Clicked on ${type} stat!`, 'success');

            switch(type) {
                case 'complete':
                    simpleShowToast('Showing incomplete profile fields...', 'info');
                    break;
                case 'listings':
                    simpleShowToast('Redirecting to listings...', 'info');
                    break;
                case 'orders':
                    simpleShowToast('Redirecting to orders...', 'info');
                    break;
                case 'member since':
                    simpleShowToast('You joined Gurumisha Motors!', 'info');
                    break;
                default:
                    simpleShowToast('Feature coming soon!', 'info');
            }
        }

        function handleContactClick(type, value) {
            console.log('Contact clicked:', type, value);

            try {
                switch(type) {
                    case 'email':
                        simpleShowToast('Would open email client...', 'info');
                        break;
                    case 'phone':
                        if (navigator.clipboard && navigator.clipboard.writeText) {
                            navigator.clipboard.writeText(value).then(() => {
                                simpleShowToast('Phone number copied!', 'success');
                            }).catch(() => {
                                simpleShowToast(`Phone: ${value}`, 'info');
                            });
                        } else {
                            simpleShowToast(`Phone: ${value}`, 'info');
                        }
                        break;
                    case 'location':
                        simpleShowToast('Would open maps...', 'info');
                        break;
                    default:
                        simpleShowToast('Contact action not supported', 'warning');
                }
            } catch(e) {
                console.error('Contact click error:', e);
                simpleShowToast('Error handling contact action', 'error');
            }
        }

        function handleBadgeClick(text) {
            console.log('Badge clicked:', text);
            simpleShowToast(`Badge: ${text}`, 'info');
        }

        // Test function
        function testClickFunctionality() {
            console.log('Testing click functionality...');
            simpleShowToast('Click test successful!', 'success');
        }

        // Tab switching function
        function switchToTab(tabName) {
            console.log('=== switchToTab called ===');
            console.log('Target tab:', tabName);

            if (!tabName) {
                console.error('No tab name provided');
                return;
            }

            try {
                // Update tab buttons - remove active state from all
                const allTabs = document.querySelectorAll('.nav-tab');
                allTabs.forEach(tab => {
                    tab.classList.remove('active');
                });

                // Set active state for target tab
                const activeTab = document.querySelector(`[data-tab="${tabName}"]`);
                if (activeTab) {
                    activeTab.classList.add('active');
                    console.log('✓ Active tab updated successfully');
                } else {
                    console.error('❌ Target tab not found:', `[data-tab="${tabName}"]`);
                }

                // Update tab content panels
                const allPanels = document.querySelectorAll('.tab-content-panel');
                allPanels.forEach(content => {
                    content.classList.remove('active');
                    content.style.display = 'none';
                });

                const targetContent = document.getElementById(`${tabName}-tab`);
                if (targetContent) {
                    targetContent.classList.add('active');
                    targetContent.style.display = 'block';
                    console.log('✓ Active content updated successfully');
                } else {
                    console.error('❌ Target content not found:', `${tabName}-tab`);
                }

                console.log('=== Tab switch completed successfully ===');
                simpleShowToast(`Switched to ${tabName} tab`, 'success');
            } catch (error) {
                console.error('❌ Error switching tabs:', error);
                simpleShowToast('Error switching tabs', 'error');
            }
        }

        // Password toggle function
        function togglePassword(fieldId) {
            console.log('Toggle password for:', fieldId);
            const field = document.getElementById(fieldId);
            
            if (field) {
                if (field.type === 'password') {
                    field.type = 'text';
                    simpleShowToast('Password shown', 'info');
                } else {
                    field.type = 'password';
                    simpleShowToast('Password hidden', 'info');
                }
            } else {
                console.error('Password field not found:', fieldId);
                simpleShowToast('Password field not found', 'error');
            }
        }

        // Form reset function
        function resetForm() {
            console.log('Reset form clicked');
            const form = document.getElementById('testForm');
            if (form) {
                form.reset();
                simpleShowToast('Form reset successfully', 'info');
            } else {
                console.error('Form not found');
                simpleShowToast('Form not found', 'error');
            }
        }

        // Image handling functions
        function removeProfilePicture() {
            console.log('Remove profile picture clicked');
            simpleShowToast('Profile picture would be removed', 'info');
        }

        function openImageCropper() {
            console.log('Open image cropper clicked');
            simpleShowToast('Image cropper would open', 'info');
        }

        function setAspectRatio(ratio) {
            console.log('Set aspect ratio:', ratio);
            simpleShowToast(`Aspect ratio set to ${ratio === 1 ? 'square' : ratio}`, 'info');
        }

        // Make functions globally available
        window.showToast = simpleShowToast;
        window.handleStatClick = handleStatClick;
        window.handleContactClick = handleContactClick;
        window.handleBadgeClick = handleBadgeClick;
        window.testClickFunctionality = testClickFunctionality;
        window.switchToTab = switchToTab;
        window.togglePassword = togglePassword;
        window.resetForm = resetForm;
        window.removeProfilePicture = removeProfilePicture;
        window.openImageCropper = openImageCropper;
        window.setAspectRatio = setAspectRatio;

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded - all functions should be available');
            console.log('Available functions:', Object.keys(window).filter(key => 
                typeof window[key] === 'function' && 
                (key.startsWith('handle') || key.startsWith('switch') || key.startsWith('simple') || key.startsWith('toggle') || key.startsWith('remove') || key.startsWith('open') || key.startsWith('set'))
            ));
        });
    </script>
</body>
</html>
