#!/usr/bin/env python
"""
M-Pesa Integration Test Script
Run this script to test M-Pesa configuration and functionality
"""

import os
import sys
import django

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gurumisha_project.settings')
django.setup()

from django.conf import settings
from core.mpesa_integration import MPesaAPI, MPesaConfig
from core.models import User, SparePart, SparePartCategory, Supplier, Vendor, Cart, CartItem, Order
from decimal import Decimal
import json


def test_configuration():
    """Test M-Pesa configuration"""
    print("🔧 Testing M-Pesa Configuration...")
    print("-" * 50)
    
    config = MPesaConfig()
    
    print(f"Environment: {config.environment}")
    print(f"Business Short Code: {config.business_short_code}")
    print(f"Consumer Key: {'✓ Set' if config.consumer_key else '✗ Missing'}")
    print(f"Consumer Secret: {'✓ Set' if config.consumer_secret else '✗ Missing'}")
    print(f"Passkey: {'✓ Set' if config.passkey else '✗ Missing'}")
    print(f"Callback URL: {config.callback_url}")
    print(f"Timeout URL: {config.timeout_url}")
    print(f"Base URL: {config.base_url}")
    print(f"Is Configured: {'✓ Yes' if config.is_configured else '✗ No'}")
    
    return config.is_configured


def test_access_token():
    """Test M-Pesa access token generation"""
    print("\n🔑 Testing Access Token Generation...")
    print("-" * 50)
    
    try:
        mpesa = MPesaAPI()
        token = mpesa.get_access_token()
        
        if token:
            print("✓ Access token generated successfully")
            print(f"Token (first 20 chars): {token[:20]}...")
            return True
        else:
            print("✗ Failed to generate access token")
            return False
    except Exception as e:
        print(f"✗ Error generating access token: {str(e)}")
        return False


def test_password_generation():
    """Test M-Pesa password generation"""
    print("\n🔐 Testing Password Generation...")
    print("-" * 50)
    
    try:
        mpesa = MPesaAPI()
        password, timestamp = mpesa.generate_password()
        
        print(f"✓ Password generated successfully")
        print(f"Timestamp: {timestamp}")
        print(f"Password (first 20 chars): {password[:20]}...")
        return True
    except Exception as e:
        print(f"✗ Error generating password: {str(e)}")
        return False


def test_stk_push():
    """Test STK Push functionality"""
    print("\n📱 Testing STK Push...")
    print("-" * 50)
    
    # Use test phone number
    test_phone = "************"  # Safaricom test number
    test_amount = 1  # Minimum amount
    
    try:
        mpesa = MPesaAPI()
        result = mpesa.initiate_stk_push(
            phone_number=test_phone,
            amount=test_amount,
            account_reference="TEST123",
            transaction_desc="Test Payment"
        )
        
        print(f"STK Push Result:")
        print(json.dumps(result, indent=2))
        
        if result.get('success'):
            print("✓ STK Push initiated successfully")
            return True
        else:
            print(f"✗ STK Push failed: {result.get('message')}")
            return False
    except Exception as e:
        print(f"✗ Error initiating STK Push: {str(e)}")
        return False


def create_test_order():
    """Create a test order for M-Pesa testing"""
    print("\n📦 Creating Test Order...")
    print("-" * 50)
    
    try:
        # Get or create test user
        user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'username': 'testuser',
                'first_name': 'Test',
                'last_name': 'User',
                'phone_number': '************',
                'is_email_verified': True
            }
        )
        
        # Get or create test vendor
        vendor, created = Vendor.objects.get_or_create(
            user=user,
            defaults={
                'business_name': 'Test Vendor',
                'business_type': 'spare_parts',
                'is_approved': True
            }
        )
        
        # Get or create test category
        category, created = SparePartCategory.objects.get_or_create(
            name='Test Category',
            defaults={'description': 'Test category for M-Pesa testing'}
        )
        
        # Get or create test supplier
        supplier, created = Supplier.objects.get_or_create(
            name='Test Supplier',
            defaults={
                'contact_person': 'Test Contact',
                'email': '<EMAIL>',
                'phone': '254700000000'
            }
        )
        
        # Get or create test spare part
        spare_part, created = SparePart.objects.get_or_create(
            name='Test Spare Part',
            sku='TEST001',
            defaults={
                'vendor': vendor,
                'category': category,
                'supplier': supplier,
                'description': 'Test spare part for M-Pesa testing',
                'price': Decimal('100.00'),
                'stock_quantity': 10,
                'is_available': True
            }
        )
        
        # Create cart and add item
        cart, created = Cart.objects.get_or_create(user=user)
        cart_item, created = CartItem.objects.get_or_create(
            cart=cart,
            spare_part=spare_part,
            defaults={
                'quantity': 1,
                'price': spare_part.price
            }
        )
        
        print(f"✓ Test order setup completed")
        print(f"User: {user.email}")
        print(f"Spare Part: {spare_part.name}")
        print(f"Price: KSh {spare_part.price}")
        
        return user, cart
    except Exception as e:
        print(f"✗ Error creating test order: {str(e)}")
        return None, None


def test_full_integration():
    """Test full M-Pesa integration with order"""
    print("\n🚀 Testing Full Integration...")
    print("-" * 50)
    
    user, cart = create_test_order()
    if not user or not cart:
        return False
    
    try:
        from core.mpesa_integration import initiate_mpesa_payment
        
        # Create a mock order (simplified)
        class MockOrder:
            def __init__(self, total_amount, order_number):
                self.total_amount = total_amount
                self.order_number = order_number
        
        mock_order = MockOrder(Decimal('100.00'), 'TEST001')
        
        result = initiate_mpesa_payment(mock_order, '************')
        
        print(f"Full Integration Result:")
        print(json.dumps(result, indent=2, default=str))
        
        if result.get('success'):
            print("✓ Full integration test successful")
            return True
        else:
            print(f"✗ Full integration test failed: {result.get('message')}")
            return False
    except Exception as e:
        print(f"✗ Error in full integration test: {str(e)}")
        return False


def main():
    """Run all M-Pesa tests"""
    print("🧪 M-Pesa Integration Test Suite")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_configuration),
        ("Access Token", test_access_token),
        ("Password Generation", test_password_generation),
        ("STK Push", test_stk_push),
        # ("Full Integration", test_full_integration),  # Uncomment for full test
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test crashed: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 50)
    passed = 0
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("🎉 All tests passed! M-Pesa integration is ready.")
    else:
        print("⚠️  Some tests failed. Please check your configuration.")
        print("\n💡 Next Steps:")
        print("1. Verify your M-Pesa credentials in .env file")
        print("2. Ensure you have internet connection")
        print("3. Check if you're using correct sandbox/production environment")
        print("4. Review the M-Pesa setup guide: MPESA_SETUP_GUIDE.md")


if __name__ == "__main__":
    main()
