<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Sell Car Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-section {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            margin: 20px 0;
            border-radius: 12px;
            border: 1px solid #e0e0e0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input:focus, select:focus, textarea:focus {
            border-color: #DC2626;
            outline: none;
        }
        .btn {
            background: #DC2626;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background: #B91C1C;
        }
        .visible-check {
            background: #10B981;
            color: white;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="visible-check">
            ✅ This test form is visible! If you can see this, the basic styling is working.
        </div>
        
        <h1>Sell Car Form Test</h1>
        <p>This is a simplified version to test if the form structure works correctly.</p>
        
        <form>
            <div class="form-section">
                <h3>Basic Information</h3>
                <div class="form-group">
                    <label for="title">Listing Title</label>
                    <input type="text" id="title" name="title" placeholder="e.g., 2020 Toyota Camry - Excellent Condition">
                </div>
                <div class="form-group">
                    <label for="price">Price (KES)</label>
                    <input type="number" id="price" name="price" placeholder="2500000">
                </div>
            </div>
            
            <div class="form-section">
                <h3>Vehicle Details</h3>
                <div class="form-group">
                    <label for="brand">Brand</label>
                    <select id="brand" name="brand">
                        <option value="">Select a brand</option>
                        <option value="toyota">Toyota</option>
                        <option value="honda">Honda</option>
                        <option value="bmw">BMW</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="model">Model</label>
                    <select id="model" name="model">
                        <option value="">Select a model</option>
                        <option value="camry">Camry</option>
                        <option value="civic">Civic</option>
                        <option value="x5">X5</option>
                    </select>
                </div>
            </div>
            
            <div class="form-section">
                <h3>Description</h3>
                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea id="description" name="description" rows="4" placeholder="Describe your car in detail..."></textarea>
                </div>
            </div>
            
            <button type="submit" class="btn">Submit Test Form</button>
        </form>
    </div>
</body>
</html>
