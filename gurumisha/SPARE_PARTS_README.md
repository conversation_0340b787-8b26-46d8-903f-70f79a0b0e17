# Gurumisha Motors - Comprehensive Spare Parts System

## Overview

The Gurumisha Motors spare parts system is a comprehensive e-commerce solution for automotive spare parts with advanced features including M-Pesa payment integration, real-time inventory management, and modern UI/UX design following harrier design patterns.

## Features Implemented

### ✅ Core Functionality
- **Complete Spare Parts Models**: Comprehensive database schema with inventory management
- **Enhanced Admin Dashboard**: Full CRUD operations with analytics and reporting
- **Customer Interface**: Modern browsing experience with categories, search, and filtering
- **M-Pesa Payment Integration**: Real Safaricom API integration with proper error handling
- **HTMX Dynamic Features**: Real-time updates and interactive elements
- **Harrier Design Patterns**: Glassmorphism effects, smooth animations, mobile-responsive design

### ✅ Admin Features
- Enhanced spare parts management dashboard
- Real-time inventory tracking and alerts
- Supplier management system
- Purchase order workflow
- Stock movement tracking
- Comprehensive analytics and reporting
- CSV/Excel export functionality
- Bulk operations support

### ✅ Customer Features
- Category-based browsing with visual showcase
- Advanced search and filtering
- Quick view modals for products
- Shopping cart with real-time updates
- M-Pesa payment integration
- Order tracking and history
- Mobile-responsive design

### ✅ Technical Features
- HTMX for dynamic interactions
- Real-time search and filtering
- Barcode/SKU tracking system
- Inventory alerts and notifications
- Comprehensive test coverage
- Management commands for data population

## File Structure

```
gurumisha/
├── core/
│   ├── models.py                     # Enhanced spare parts models
│   ├── views.py                      # Main views with HTMX imports
│   ├── forms.py                      # Enhanced forms for spare parts
│   ├── urls.py                       # URL patterns including HTMX endpoints
│   ├── mpesa_integration.py          # Complete M-Pesa API integration
│   ├── htmx_spare_parts_views.py     # HTMX-specific views
│   ├── admin_spare_parts_views.py    # Enhanced admin views
│   ├── management/commands/
│   │   └── populate_spare_parts.py   # Data population command
│   └── tests/
│       └── test_spare_parts.py       # Comprehensive test suite
├── templates/core/
│   ├── spare_parts.html              # Enhanced main spare parts page
│   ├── spare_part_detail.html        # Product detail page
│   ├── cart.html                     # Shopping cart page
│   ├── checkout.html                 # Checkout process
│   ├── dashboard/
│   │   ├── admin_spare_parts_enhanced.html  # Enhanced admin dashboard
│   │   └── admin_spare_part_detail.html     # Admin detail view
│   └── partials/
│       ├── spare_parts_grid.html     # Enhanced product grid
│       ├── spare_part_quick_view.html # Quick view modal
│       ├── cart_success.html         # Cart success messages
│       └── cart_error.html           # Cart error messages
└── gurumisha/
    └── mpesa_settings.py             # M-Pesa configuration
```

## Models Overview

### Core Models
- **SparePart**: Main product model with comprehensive fields
- **SparePartCategory**: Hierarchical category system
- **Supplier**: Supplier management with ratings
- **Cart/CartItem**: Shopping cart functionality
- **Order/OrderItem**: Order management system
- **Payment**: Payment tracking with M-Pesa integration
- **StockMovement**: Inventory tracking
- **InventoryAlert**: Low stock notifications

## Setup Instructions

### 1. Database Migration
```bash
python manage.py makemigrations
python manage.py migrate
```

### 2. Create Test Data
```bash
# Populate spare parts with test data
python manage.py populate_spare_parts --count 100

# Clear existing data and repopulate
python manage.py populate_spare_parts --count 50 --clear
```

### 3. M-Pesa Configuration
Create a `.env` file with your M-Pesa credentials:

```env
# M-Pesa Sandbox Configuration
MPESA_SANDBOX_CONSUMER_KEY=your_sandbox_consumer_key
MPESA_SANDBOX_CONSUMER_SECRET=your_sandbox_consumer_secret
MPESA_SANDBOX_BUSINESS_SHORT_CODE=174379
MPESA_SANDBOX_PASSKEY=your_sandbox_passkey

# M-Pesa Production Configuration
MPESA_PRODUCTION_CONSUMER_KEY=your_production_consumer_key
MPESA_PRODUCTION_CONSUMER_SECRET=your_production_consumer_secret
MPESA_PRODUCTION_BUSINESS_SHORT_CODE=your_production_shortcode
MPESA_PRODUCTION_PASSKEY=your_production_passkey

# Environment (sandbox or production)
MPESA_ENVIRONMENT=sandbox

# Callback URLs
MPESA_CALLBACK_URL=https://yourdomain.com/payments/mpesa/callback/
```

### 4. Static Files
```bash
python manage.py collectstatic
```

## Usage Guide

### Admin Dashboard
1. Navigate to `/admin/spare-parts/` for the enhanced admin dashboard
2. Use filters and search to manage inventory
3. Click on parts for detailed views and stock management
4. Export data using the export buttons

### Customer Interface
1. Visit `/spare-parts/` for the main spare parts page
2. Use category showcase for quick filtering
3. Search and filter using the advanced filter form
4. Click "Quick View" for product previews
5. Add items to cart and proceed to checkout

### M-Pesa Payments
1. During checkout, select M-Pesa payment method
2. Enter your phone number (254XXXXXXXXX format)
3. Complete payment on your phone
4. System automatically updates order status

## API Endpoints

### HTMX Endpoints
- `GET /spare-parts/live-search/` - Real-time search
- `GET /spare-parts/quick-view/<id>/` - Quick view modal
- `POST /cart/add-htmx/` - Add to cart
- `POST /cart/update-htmx/` - Update cart quantity
- `GET /spare-parts/category/<id>/` - Filter by category
- `GET /spare-parts/stats/` - Real-time statistics

### Payment Endpoints
- `POST /payments/mpesa/callback/` - M-Pesa callback handler

## Testing

### Run Tests
```bash
# Run all spare parts tests
python manage.py test core.tests.test_spare_parts

# Run specific test class
python manage.py test core.tests.test_spare_parts.SparePartModelTest

# Run with coverage
coverage run --source='.' manage.py test core.tests.test_spare_parts
coverage report
```

### Test Coverage
- Model functionality and validation
- View responses and authentication
- Form validation and processing
- M-Pesa integration
- Cart functionality
- Stock movement tracking
- API endpoints

## Design Patterns

### Harrier Design System
- **Colors**: Red (#DC2626), Black (#000000), Dark Blue (#1E3A8A), White (#FFFFFF)
- **Typography**: Montserrat (headings), Raleway (body text)
- **Effects**: Glassmorphism, smooth transitions, micro-animations
- **Layout**: Mobile-first responsive design

### CSS Features
- Cubic-bezier transitions for smooth animations
- Hover effects with transform and shadow
- Loading states with shimmer effects
- Toast notifications with backdrop blur
- Custom scrollbars with harrier colors

## Performance Optimizations

- **Database**: Optimized queries with select_related and prefetch_related
- **Caching**: M-Pesa token caching for API efficiency
- **Pagination**: 20 items per page for optimal loading
- **Images**: Lazy loading for product images
- **HTMX**: Partial page updates for better UX

## Security Features

- **Authentication**: Role-based access control
- **CSRF Protection**: All forms include CSRF tokens
- **Input Validation**: Comprehensive form validation
- **SQL Injection**: Django ORM prevents SQL injection
- **XSS Protection**: Template auto-escaping enabled

## Monitoring and Logging

- **Stock Alerts**: Automatic low stock notifications
- **Payment Tracking**: Complete M-Pesa transaction logging
- **Error Handling**: Comprehensive error messages
- **Activity Logs**: Stock movement tracking

## Future Enhancements

### Planned Features
- [ ] Barcode scanning functionality
- [ ] Advanced analytics dashboard
- [ ] Automated reordering system
- [ ] Multi-vendor marketplace features
- [ ] Mobile app integration
- [ ] Advanced reporting tools

### Technical Improvements
- [ ] Redis caching implementation
- [ ] Elasticsearch for advanced search
- [ ] WebSocket for real-time updates
- [ ] API rate limiting
- [ ] Advanced image optimization

## Support and Maintenance

### Regular Tasks
- Monitor stock levels and alerts
- Review M-Pesa transaction logs
- Update product information
- Manage supplier relationships
- Analyze sales performance

### Troubleshooting
- Check M-Pesa configuration if payments fail
- Verify HTMX endpoints for dynamic features
- Monitor server logs for errors
- Test responsive design on various devices

## Contributing

1. Follow Django best practices
2. Maintain harrier design patterns
3. Write comprehensive tests
4. Document new features
5. Ensure mobile responsiveness

## License

This spare parts system is part of the Gurumisha Motors platform and follows the project's licensing terms.

---

**Note**: This system is production-ready with comprehensive testing, proper error handling, and security measures. All features have been implemented following Django best practices and modern web development standards.
