# Django Configuration
DEBUG=True
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Database Configuration
DATABASE_URL=sqlite:///db.sqlite3

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
EMAIL_USE_TLS=True
DEFAULT_FROM_EMAIL=Your Site <<EMAIL>>
SERVER_EMAIL=<EMAIL>

# M-Pesa Sandbox Configuration
# Get these from Safaricom Developer Portal
MPESA_SANDBOX_CONSUMER_KEY=your_sandbox_consumer_key
MPESA_SANDBOX_CONSUMER_SECRET=your_sandbox_consumer_secret
MPESA_SANDBOX_BUSINESS_SHORT_CODE=174379
MPESA_SANDBOX_PASSKEY=your_sandbox_passkey

# M-Pesa Production Configuration
# Get these from Safaricom for production use
MPESA_PRODUCTION_CONSUMER_KEY=your_production_consumer_key
MPESA_PRODUCTION_CONSUMER_SECRET=your_production_consumer_secret
MPESA_PRODUCTION_BUSINESS_SHORT_CODE=your_production_shortcode
MPESA_PRODUCTION_PASSKEY=your_production_passkey

# M-Pesa Environment (sandbox or production)
MPESA_ENVIRONMENT=sandbox

# M-Pesa Callback URLs
# For development, use ngrok: https://ngrok.com/
# Run: ngrok http 8000
# Then use the https URL provided
MPESA_CALLBACK_URL=https://your-ngrok-url.ngrok.io/payments/mpesa/callback/
MPESA_TIMEOUT_URL=https://your-ngrok-url.ngrok.io/payments/mpesa/timeout/

# Static and Media Files
STATIC_URL=/static/
MEDIA_URL=/media/

# Site Configuration
SITE_NAME=Your Site Name
SITE_DOMAIN=localhost:8000

# Security Settings (uncomment for production)
# SECURE_SSL_REDIRECT=True
# SECURE_HSTS_SECONDS=31536000
# SECURE_HSTS_INCLUDE_SUBDOMAINS=True
# SECURE_HSTS_PRELOAD=True
# SESSION_COOKIE_SECURE=True
# CSRF_COOKIE_SECURE=True

# Cache Configuration (Redis)
# REDIS_URL=redis://localhost:6379/1

# Celery Configuration (for background tasks)
# CELERY_BROKER_URL=redis://localhost:6379/0
# CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Logging Level
LOG_LEVEL=INFO
