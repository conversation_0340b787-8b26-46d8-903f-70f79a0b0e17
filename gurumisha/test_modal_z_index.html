<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Z-Index Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Test the z-index hierarchy */
        .test-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 0, 0, 0.3);
            z-index: 1000;
        }
        
        .test-shadow {
            position: fixed;
            top: 50px;
            left: 50px;
            width: 200px;
            height: 200px;
            background: rgba(0, 0, 255, 0.5);
            z-index: 1200;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        
        /* Admin modal styles - should appear above everything */
        .admin-modal {
            z-index: 1500;
        }
        
        .admin-modal-backdrop {
            z-index: 1400;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-bold mb-8">Modal Z-Index Test</h1>
        
        <div class="space-y-4">
            <button onclick="showTestOverlay()" class="bg-red-500 text-white px-4 py-2 rounded">
                Show Test Overlay (z-1000)
            </button>
            
            <button onclick="showTestShadow()" class="bg-blue-500 text-white px-4 py-2 rounded">
                Show Test Shadow (z-1200)
            </button>
            
            <button onclick="showAdminModal()" class="bg-green-500 text-white px-4 py-2 rounded">
                Show Admin Modal (z-1500)
            </button>
            
            <button onclick="clearAll()" class="bg-gray-500 text-white px-4 py-2 rounded">
                Clear All
            </button>
        </div>
        
        <div class="mt-8 p-4 bg-white rounded shadow">
            <h2 class="text-xl font-semibold mb-4">Z-Index Hierarchy Test</h2>
            <p class="mb-2">This test verifies the z-index hierarchy:</p>
            <ul class="list-disc list-inside space-y-1">
                <li>Base content: 1-10</li>
                <li>Overlays/dropdowns: 100-999</li>
                <li>Profile modals: 1000-1199</li>
                <li>Admin modals: 1400-1599</li>
                <li>Notifications/toasts: 9000-9999</li>
            </ul>
        </div>
    </div>
    
    <!-- Test Overlay -->
    <div id="test-overlay" class="test-overlay hidden">
        <div class="flex items-center justify-center h-full">
            <div class="bg-white p-8 rounded shadow-lg">
                <h3 class="text-lg font-semibold">Test Overlay (z-1000)</h3>
                <p>This should appear behind admin modals</p>
            </div>
        </div>
    </div>
    
    <!-- Test Shadow -->
    <div id="test-shadow" class="test-shadow hidden">
        <div class="p-4 text-white">
            <h4>Shadow Element</h4>
            <p>z-1200</p>
        </div>
    </div>
    
    <!-- Admin Modal Test -->
    <div id="admin-modal" class="fixed inset-0 admin-modal overflow-y-auto hidden">
        <!-- Modal Backdrop -->
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity admin-modal-backdrop"></div>
        
        <!-- Modal Container -->
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl max-w-md w-full shadow-2xl relative">
                <!-- Modal Header -->
                <div class="bg-gradient-to-r from-red-600 to-red-800 px-6 py-4 rounded-t-2xl">
                    <div class="flex items-center justify-between">
                        <h3 class="text-xl font-bold text-white">Admin Modal Test</h3>
                        <button onclick="hideAdminModal()" class="text-white hover:text-gray-200">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Modal Body -->
                <div class="p-6">
                    <p class="mb-4">This admin modal should appear above all other elements including:</p>
                    <ul class="list-disc list-inside space-y-1">
                        <li>Test overlay (z-1000)</li>
                        <li>Test shadow (z-1200)</li>
                        <li>Any other overlays or shadows</li>
                    </ul>
                    <p class="mt-4 text-sm text-gray-600">Z-index: 1500 (modal), 1400 (backdrop)</p>
                </div>
                
                <!-- Modal Footer -->
                <div class="bg-gray-50 px-6 py-4 rounded-b-2xl">
                    <button onclick="hideAdminModal()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTestOverlay() {
            document.getElementById('test-overlay').classList.remove('hidden');
        }
        
        function showTestShadow() {
            document.getElementById('test-shadow').classList.remove('hidden');
        }
        
        function showAdminModal() {
            document.getElementById('admin-modal').classList.remove('hidden');
        }
        
        function hideAdminModal() {
            document.getElementById('admin-modal').classList.add('hidden');
        }
        
        function clearAll() {
            document.getElementById('test-overlay').classList.add('hidden');
            document.getElementById('test-shadow').classList.add('hidden');
            document.getElementById('admin-modal').classList.add('hidden');
        }
    </script>
</body>
</html>
