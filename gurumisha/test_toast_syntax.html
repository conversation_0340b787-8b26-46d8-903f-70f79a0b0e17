<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Toast Manager Syntax Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        button {
            background: #dc2626;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #b91c1c;
        }
    </style>
</head>
<body>
    <h1>Toast Manager Syntax Test</h1>
    
    <div id="test-results"></div>
    
    <button onclick="testToast()">Test Toast</button>
    <button onclick="testToastManager()">Test Toast Manager</button>

    <script>
        const results = document.getElementById('test-results');
        
        function addResult(message, type) {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            results.appendChild(div);
        }

        // Capture any JavaScript errors
        window.addEventListener('error', function(e) {
            addResult('JavaScript Error: ' + e.error.message + ' at line ' + e.lineno, 'error');
        });

        // Test loading the toast manager
        addResult('Starting toast manager syntax test...', 'info');
    </script>

    <!-- Load the fixed toast manager -->
    <script src="/static/js/toast-manager.js"></script>

    <script>
        // Test after loading
        setTimeout(() => {
            if (window.toastManagerLoaded) {
                addResult('✓ Toast Manager loaded successfully without syntax errors', 'success');
                
                if (window.toastManager) {
                    addResult('✓ Toast Manager instance created', 'success');
                } else {
                    addResult('⚠ Toast Manager instance not found', 'info');
                }
            } else {
                addResult('⚠ Toast Manager not loaded', 'info');
            }
        }, 100);

        function testToast() {
            try {
                if (window.toastManager && typeof window.toastManager.show === 'function') {
                    window.toastManager.show('Test toast message!', 'success');
                    addResult('✓ Toast Manager show() method working', 'success');
                } else {
                    addResult('⚠ Toast Manager show() method not available', 'info');
                }
            } catch (error) {
                addResult('✗ Toast test error: ' + error.message, 'error');
            }
        }

        function testToastManager() {
            try {
                if (window.showToast && typeof window.showToast === 'function') {
                    window.showToast('Global showToast working!', 'info');
                    addResult('✓ Global showToast function working', 'success');
                } else {
                    addResult('⚠ Global showToast function not available', 'info');
                }
            } catch (error) {
                addResult('✗ Global toast test error: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
