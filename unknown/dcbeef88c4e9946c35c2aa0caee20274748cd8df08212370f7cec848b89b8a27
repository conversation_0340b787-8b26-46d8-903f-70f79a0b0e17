# Generated by Django 5.2 on 2025-07-13 20:30

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0025_contentperformancereport_staticpage_contentanalytics'),
    ]

    operations = [
        migrations.CreateModel(
            name='MessageAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('total_displays', models.PositiveIntegerField(default=0)),
                ('unique_users_shown', models.PositiveIntegerField(default=0)),
                ('total_clicks', models.PositiveIntegerField(default=0)),
                ('unique_users_clicked', models.PositiveIntegerField(default=0)),
                ('total_dismissals', models.PositiveIntegerField(default=0)),
                ('unique_users_dismissed', models.PositiveIntegerField(default=0)),
                ('customers_shown', models.PositiveIntegerField(default=0)),
                ('vendors_shown', models.PositiveIntegerField(default=0)),
                ('admins_shown', models.PositiveIntegerField(default=0)),
                ('desktop_displays', models.PositiveIntegerField(default=0)),
                ('mobile_displays', models.PositiveIntegerField(default=0)),
                ('tablet_displays', models.PositiveIntegerField(default=0)),
                ('avg_time_to_action', models.DurationField(blank=True, null=True)),
                ('bounce_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Message Analytics',
                'verbose_name_plural': 'Message Analytics',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='MessageRead',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('viewed', 'Viewed'), ('clicked', 'Clicked'), ('dismissed', 'Dismissed'), ('ignored', 'Ignored')], default='viewed', max_length=20)),
                ('display_count', models.PositiveIntegerField(default=1, help_text='Number of times shown to user')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('referrer_url', models.URLField(blank=True)),
                ('first_seen_at', models.DateTimeField(auto_now_add=True)),
                ('last_seen_at', models.DateTimeField(auto_now=True)),
                ('action_taken_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Message Read Record',
                'verbose_name_plural': 'Message Read Records',
                'ordering': ['-last_seen_at'],
            },
        ),
        migrations.CreateModel(
            name='MessageSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('frequency', models.CharField(choices=[('once', 'One Time'), ('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('yearly', 'Yearly')], default='once', max_length=20)),
                ('send_time', models.TimeField(help_text='Time of day to send')),
                ('timezone', models.CharField(default='UTC', help_text='Timezone for scheduling', max_length=50)),
                ('weekdays', models.JSONField(blank=True, default=list, help_text='List of weekday numbers (0=Monday)')),
                ('day_of_month', models.PositiveIntegerField(blank=True, help_text='Day of month (1-31)', null=True)),
                ('max_occurrences', models.PositiveIntegerField(blank=True, help_text='Maximum number of times to send', null=True)),
                ('end_date', models.DateField(blank=True, help_text='Stop recurring after this date', null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('occurrences_sent', models.PositiveIntegerField(default=0)),
                ('last_sent_at', models.DateTimeField(blank=True, null=True)),
                ('next_send_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Message Schedule',
                'verbose_name_plural': 'Message Schedules',
                'ordering': ['next_send_at'],
            },
        ),
        migrations.CreateModel(
            name='MessageTarget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(choices=[('user.role', 'User Role'), ('user.date_joined', 'Registration Date'), ('user.last_login', 'Last Login Date'), ('user.is_email_verified', 'Email Verified'), ('user.city', 'City'), ('user.country', 'Country'), ('user.language', 'Language'), ('vendor.is_verified', 'Vendor Verified'), ('vendor.company_name', 'Company Name'), ('car_count', 'Number of Cars Listed'), ('order_count', 'Number of Orders'), ('login_count', 'Login Count')], max_length=50)),
                ('condition', models.CharField(choices=[('equals', 'Equals'), ('not_equals', 'Not Equals'), ('contains', 'Contains'), ('not_contains', 'Does Not Contain'), ('greater_than', 'Greater Than'), ('less_than', 'Less Than'), ('in_list', 'In List'), ('not_in_list', 'Not In List')], max_length=20)),
                ('value', models.TextField(help_text='Value to compare against')),
                ('is_required', models.BooleanField(default=True, help_text='Must match for user to see message')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Message Target Rule',
                'verbose_name_plural': 'Message Target Rules',
                'ordering': ['message', 'field_name'],
            },
        ),
        migrations.CreateModel(
            name='MessageTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('category', models.CharField(choices=[('announcement', 'Announcements'), ('promotion', 'Promotions'), ('maintenance', 'Maintenance'), ('welcome', 'Welcome Messages'), ('feature', 'Feature Updates'), ('policy', 'Policy Updates'), ('seasonal', 'Seasonal Messages')], max_length=20)),
                ('description', models.TextField(blank=True)),
                ('title_template', models.CharField(help_text='Use {{variable}} for dynamic content', max_length=200)),
                ('content_template', models.TextField(help_text='Use {{variable}} for dynamic content')),
                ('default_message_type', models.CharField(choices=[('announcement', 'Announcement'), ('newsletter', 'Newsletter'), ('alert', 'Alert'), ('promotion', 'Promotion'), ('maintenance', 'Maintenance Notice'), ('feature', 'Feature Update'), ('policy', 'Policy Update'), ('welcome', 'Welcome Message')], max_length=20)),
                ('default_target_audience', models.CharField(choices=[('all', 'All Users'), ('customers', 'Customers Only'), ('vendors', 'Vendors Only'), ('admins', 'Admins Only'), ('new_users', 'New Users (< 30 days)'), ('active_users', 'Active Users'), ('inactive_users', 'Inactive Users')], max_length=20)),
                ('default_priority', models.IntegerField(choices=[(1, 'Low'), (2, 'Normal'), (3, 'High'), (4, 'Critical')], default=2)),
                ('default_background_color', models.CharField(default='#ffffff', max_length=7)),
                ('default_text_color', models.CharField(default='#000000', max_length=7)),
                ('default_icon_class', models.CharField(blank=True, max_length=50)),
                ('available_variables', models.JSONField(default=list, help_text='List of available template variables')),
                ('usage_count', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Message Template',
                'verbose_name_plural': 'Message Templates',
                'ordering': ['category', 'name'],
            },
        ),
        migrations.AlterModelOptions(
            name='message',
            options={'ordering': ['-priority', '-created_at'], 'verbose_name': 'Message', 'verbose_name_plural': 'Messages'},
        ),
        migrations.RemoveField(
            model_name='message',
            name='inquiry',
        ),
        migrations.RemoveField(
            model_name='message',
            name='is_read',
        ),
        migrations.RemoveField(
            model_name='message',
            name='recipient',
        ),
        migrations.RemoveField(
            model_name='message',
            name='sender',
        ),
        migrations.AddField(
            model_name='message',
            name='action_button_color',
            field=models.CharField(default='#dc2626', help_text='Button color (hex)', max_length=7),
        ),
        migrations.AddField(
            model_name='message',
            name='action_button_text',
            field=models.CharField(blank=True, help_text='Call-to-action button text', max_length=50),
        ),
        migrations.AddField(
            model_name='message',
            name='action_button_url',
            field=models.URLField(blank=True, help_text='URL for action button'),
        ),
        migrations.AddField(
            model_name='message',
            name='auto_dismiss_seconds',
            field=models.PositiveIntegerField(blank=True, help_text='Auto-dismiss after X seconds', null=True),
        ),
        migrations.AddField(
            model_name='message',
            name='background_color',
            field=models.CharField(default='#ffffff', help_text='Hex color code', max_length=7),
        ),
        migrations.AddField(
            model_name='message',
            name='created_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='created_messages', to=settings.AUTH_USER_MODEL),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='message',
            name='display_frequency_hours',
            field=models.PositiveIntegerField(default=24, help_text='Minimum hours between displays'),
        ),
        migrations.AddField(
            model_name='message',
            name='excerpt',
            field=models.TextField(blank=True, help_text='Brief summary for previews'),
        ),
        migrations.AddField(
            model_name='message',
            name='expiration_date',
            field=models.DateTimeField(blank=True, help_text='When to stop showing', null=True),
        ),
        migrations.AddField(
            model_name='message',
            name='featured_image',
            field=models.ImageField(blank=True, upload_to='messages/images/'),
        ),
        migrations.AddField(
            model_name='message',
            name='featured_image_alt',
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AddField(
            model_name='message',
            name='icon_class',
            field=models.CharField(blank=True, help_text='Font Awesome icon class', max_length=50),
        ),
        migrations.AddField(
            model_name='message',
            name='max_displays_per_user',
            field=models.PositiveIntegerField(default=1, help_text='Maximum times to show to each user'),
        ),
        migrations.AddField(
            model_name='message',
            name='max_user_age_days',
            field=models.PositiveIntegerField(blank=True, help_text='Maximum user account age in days', null=True),
        ),
        migrations.AddField(
            model_name='message',
            name='message_type',
            field=models.CharField(choices=[('announcement', 'Announcement'), ('newsletter', 'Newsletter'), ('alert', 'Alert'), ('promotion', 'Promotion'), ('maintenance', 'Maintenance Notice'), ('feature', 'Feature Update'), ('policy', 'Policy Update'), ('welcome', 'Welcome Message')], default='announcement', max_length=20),
        ),
        migrations.AddField(
            model_name='message',
            name='min_user_age_days',
            field=models.PositiveIntegerField(blank=True, help_text='Minimum user account age in days', null=True),
        ),
        migrations.AddField(
            model_name='message',
            name='priority',
            field=models.IntegerField(choices=[(1, 'Low'), (2, 'Normal'), (3, 'High'), (4, 'Critical')], default=2),
        ),
        migrations.AddField(
            model_name='message',
            name='publication_date',
            field=models.DateTimeField(blank=True, help_text='When to start showing', null=True),
        ),
        migrations.AddField(
            model_name='message',
            name='require_email_verified',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='message',
            name='show_as_banner',
            field=models.BooleanField(default=False, help_text='Show as banner notification'),
        ),
        migrations.AddField(
            model_name='message',
            name='show_as_popup',
            field=models.BooleanField(default=True, help_text='Show as popup modal'),
        ),
        migrations.AddField(
            model_name='message',
            name='show_in_dashboard',
            field=models.BooleanField(default=True, help_text='Show in user dashboard'),
        ),
        migrations.AddField(
            model_name='message',
            name='status',
            field=models.CharField(choices=[('draft', 'Draft'), ('scheduled', 'Scheduled'), ('active', 'Active'), ('paused', 'Paused'), ('expired', 'Expired'), ('archived', 'Archived')], default='draft', max_length=20),
        ),
        migrations.AddField(
            model_name='message',
            name='target_audience',
            field=models.CharField(choices=[('all', 'All Users'), ('customers', 'Customers Only'), ('vendors', 'Vendors Only'), ('admins', 'Admins Only'), ('new_users', 'New Users (< 30 days)'), ('active_users', 'Active Users'), ('inactive_users', 'Inactive Users')], default='all', max_length=20),
        ),
        migrations.AddField(
            model_name='message',
            name='text_color',
            field=models.CharField(default='#000000', help_text='Hex color code', max_length=7),
        ),
        migrations.AddField(
            model_name='message',
            name='title',
            field=models.CharField(default='Legacy Message', help_text='Message title/headline', max_length=200),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='message',
            name='total_clicks',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='message',
            name='total_dismissals',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='message',
            name='total_views',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='message',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='message',
            name='content',
            field=models.TextField(help_text='Message content (HTML supported)'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['status', 'target_audience'], name='core_messag_status_224711_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['publication_date', 'expiration_date'], name='core_messag_publica_769d9c_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['message_type', 'status'], name='core_messag_message_169824_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['-priority', '-created_at'], name='core_messag_priorit_64f588_idx'),
        ),
        migrations.AddField(
            model_name='messageanalytics',
            name='message',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='daily_analytics', to='core.message'),
        ),
        migrations.AddField(
            model_name='messageread',
            name='message',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='read_records', to='core.message'),
        ),
        migrations.AddField(
            model_name='messageread',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='message_reads', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='messageschedule',
            name='message',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='schedule', to='core.message'),
        ),
        migrations.AddField(
            model_name='messagetarget',
            name='message',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='targeting_rules', to='core.message'),
        ),
        migrations.AddField(
            model_name='messagetemplate',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='message_templates', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='messageanalytics',
            index=models.Index(fields=['message', '-date'], name='core_messag_message_a9d55f_idx'),
        ),
        migrations.AddIndex(
            model_name='messageanalytics',
            index=models.Index(fields=['-date'], name='core_messag_date_def7ce_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='messageanalytics',
            unique_together={('message', 'date')},
        ),
        migrations.AddIndex(
            model_name='messageread',
            index=models.Index(fields=['message', 'action'], name='core_messag_message_16c594_idx'),
        ),
        migrations.AddIndex(
            model_name='messageread',
            index=models.Index(fields=['user', '-last_seen_at'], name='core_messag_user_id_65412a_idx'),
        ),
        migrations.AddIndex(
            model_name='messageread',
            index=models.Index(fields=['action', '-action_taken_at'], name='core_messag_action_09c5de_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='messageread',
            unique_together={('message', 'user')},
        ),
        migrations.AddIndex(
            model_name='messageschedule',
            index=models.Index(fields=['is_active', 'next_send_at'], name='core_messag_is_acti_b3735d_idx'),
        ),
        migrations.AddIndex(
            model_name='messageschedule',
            index=models.Index(fields=['frequency', 'is_active'], name='core_messag_frequen_553ac5_idx'),
        ),
        migrations.AddIndex(
            model_name='messagetarget',
            index=models.Index(fields=['message', 'is_required'], name='core_messag_message_fe5090_idx'),
        ),
        migrations.AddIndex(
            model_name='messagetemplate',
            index=models.Index(fields=['category', 'is_active'], name='core_messag_categor_36ea37_idx'),
        ),
        migrations.AddIndex(
            model_name='messagetemplate',
            index=models.Index(fields=['-usage_count'], name='core_messag_usage_c_5ec217_idx'),
        ),
    ]
