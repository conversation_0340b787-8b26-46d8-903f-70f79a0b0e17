<!-- Cart Success Response -->
<div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4" id="cart-success-message">
    <div class="flex items-center">
        <div class="flex-shrink-0">
            <i class="fas fa-check-circle text-green-400 text-xl"></i>
        </div>
        <div class="ml-3">
            <p class="text-sm font-medium text-green-800">
                {{ message }}
            </p>
        </div>
        <div class="ml-auto pl-3">
            <button onclick="this.parentElement.parentElement.parentElement.remove()" 
                    class="text-green-400 hover:text-green-600 transition-colors">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</div>

<!-- Update cart badge if exists -->
<script>
    // Update cart badge
    const cartBadge = document.getElementById('cart-badge');
    if (cartBadge) {
        cartBadge.textContent = '{{ cart_total_items }}';
        cartBadge.style.display = '{{ cart_total_items }}' > 0 ? 'inline' : 'none';
        
        // Add animation
        cartBadge.classList.add('animate-pulse');
        setTimeout(() => {
            cartBadge.classList.remove('animate-pulse');
        }, 1000);
    }
    
    // Update add to cart button if on detail page
    const addToCartBtn = document.getElementById('add-to-cart-btn');
    if (addToCartBtn) {
        addToCartBtn.innerHTML = '<i class="fas fa-cart-plus mr-2"></i>Update Cart ({{ new_quantity }})';
    }
    
    // Auto-hide success message after 3 seconds
    setTimeout(() => {
        const successMessage = document.getElementById('cart-success-message');
        if (successMessage) {
            successMessage.style.opacity = '0';
            successMessage.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                successMessage.remove();
            }, 300);
        }
    }, 3000);
</script>
