<!-- Cart Error Response -->
<div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4" id="cart-error-message">
    <div class="flex items-center">
        <div class="flex-shrink-0">
            <i class="fas fa-exclamation-triangle text-red-400 text-xl"></i>
        </div>
        <div class="ml-3">
            <p class="text-sm font-medium text-red-800">
                {{ message }}
            </p>
        </div>
        <div class="ml-auto pl-3">
            <button onclick="this.parentElement.parentElement.parentElement.remove()" 
                    class="text-red-400 hover:text-red-600 transition-colors">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</div>

<script>
    // Auto-hide error message after 5 seconds
    setTimeout(() => {
        const errorMessage = document.getElementById('cart-error-message');
        if (errorMessage) {
            errorMessage.style.opacity = '0';
            errorMessage.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                errorMessage.remove();
            }, 300);
        }
    }, 5000);
</script>
